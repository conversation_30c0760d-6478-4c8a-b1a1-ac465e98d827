import 'package:flutter/material.dart';
import 'package:country_flags/country_flags.dart' as cf;

class CountryFlag extends StatelessWidget {
  final String? countryCode;
  final double size;
  final BorderRadius? borderRadius;

  const CountryFlag({
    super.key,
    this.countryCode,
    this.size = 20,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    if (countryCode == null || countryCode!.isEmpty) {
      return SizedBox(width: size, height: size);
    }

    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(4),
      child: cf.CountryFlag.fromCountryCode(
        countryCode!,
        height: size,
        width: size,
      ),
    );
  }
}

// Helper class to get country code from country name
class CountryHelper {
  static const Map<String, String> _countryNameToCode = {
    'Afghanistan': 'AF',
    'Albania': 'AL',
    'Algeria': 'DZ',
    'Argentina': 'AR',
    'Australia': 'AU',
    'Austria': 'AT',
    'Bangladesh': 'BD',
    'Belgium': 'BE',
    'Brazil': 'BR',
    'Canada': 'CA',
    'China': 'CN',
    'Denmark': 'DK',
    'Egypt': 'EG',
    'Finland': 'FI',
    'France': 'FR',
    'Germany': 'DE',
    'Greece': 'GR',
    'India': 'IN',
    'Indonesia': 'ID',
    'Iran': 'IR',
    'Iraq': 'IQ',
    'Ireland': 'IE',
    'Italy': 'IT',
    'Japan': 'JP',
    'Jordan': 'JO',
    'Kenya': 'KE',
    'Kuwait': 'KW',
    'Lebanon': 'LB',
    'Malaysia': 'MY',
    'Mexico': 'MX',
    'Morocco': 'MA',
    'Nepal': 'NP',
    'Netherlands': 'NL',
    'New Zealand': 'NZ',
    'Nigeria': 'NG',
    'Norway': 'NO',
    'Pakistan': 'PK',
    'Philippines': 'PH',
    'Poland': 'PL',
    'Portugal': 'PT',
    'Qatar': 'QA',
    'Russia': 'RU',
    'Saudi Arabia': 'SA',
    'Singapore': 'SG',
    'South Africa': 'ZA',
    'South Korea': 'KR',
    'Spain': 'ES',
    'Sri Lanka': 'LK',
    'Sweden': 'SE',
    'Switzerland': 'CH',
    'Thailand': 'TH',
    'Turkey': 'TR',
    'Ukraine': 'UA',
    'United Arab Emirates': 'AE',
    'United Kingdom': 'GB',
    'United States': 'US',
    'Vietnam': 'VN',
    // Add more countries as needed
  };

  static String? getCountryCode(String? countryName) {
    if (countryName == null || countryName.isEmpty) return null;
    return _countryNameToCode[countryName];
  }

  static String? getCountryName(String? countryCode) {
    if (countryCode == null || countryCode.isEmpty) return null;
    
    for (final entry in _countryNameToCode.entries) {
      if (entry.value == countryCode) {
        return entry.key;
      }
    }
    return null;
  }

  static List<String> get allCountries => _countryNameToCode.keys.toList()..sort();
}
