import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:amal_point_app/services/post_service.dart';
import 'package:amal_point_app/models/post_model.dart';
import 'package:amal_point_app/models/user_model.dart';

void main() {
  group('Post Creation Tests', () {
    test('should create PostModel correctly', () {
      final now = DateTime.now();
      final user = UserModel(
        id: 'test_user_id',
        email: '<EMAIL>',
        username: 'testuser',
        displayName: 'Test User',
        profileImageUrl: null,
        coverImageUrl: null,
        bio: null,
        gender: null,
        address: null,
        country: null,
        isVerified: false,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      );

      final post = PostModel(
        id: 'test_post_id',
        userId: user.id,
        username: user.username,
        userDisplayName: user.displayName,
        userProfileImageUrl: user.profileImageUrl,
        content: 'This is a test post',
        imageUrls: ['https://example.com/image1.jpg'],
        createdAt: now,
        updatedAt: now,
      );

      expect(post.id, equals('test_post_id'));
      expect(post.userId, equals(user.id));
      expect(post.content, equals('This is a test post'));
      expect(post.hasImages, isTrue);
      expect(post.likeCount, equals(0));
      expect(post.commentCount, equals(0));
      expect(post.shareCount, equals(0));
    });

    test('should convert PostModel to Map correctly', () {
      final now = DateTime.now();
      final post = PostModel(
        id: 'test_post_id',
        userId: 'test_user_id',
        username: 'testuser',
        userDisplayName: 'Test User',
        content: 'Test content',
        createdAt: now,
        updatedAt: now,
      );

      final map = post.toMap();

      expect(map['id'], equals('test_post_id'));
      expect(map['userId'], equals('test_user_id'));
      expect(map['username'], equals('testuser'));
      expect(map['userDisplayName'], equals('Test User'));
      expect(map['content'], equals('Test content'));
      expect(map['imageUrls'], equals([]));
      expect(map['likes'], equals([]));
      expect(map['comments'], equals([]));
      expect(map['shares'], equals([]));
      expect(map['isActive'], equals(true));
    });

    test('should create PostModel from Map correctly', () {
      final now = DateTime.now();
      final map = {
        'id': 'test_post_id',
        'userId': 'test_user_id',
        'username': 'testuser',
        'userDisplayName': 'Test User',
        'userProfileImageUrl': null,
        'content': 'Test content',
        'imageUrls': ['https://example.com/image1.jpg'],
        'createdAt': Timestamp.fromDate(now),
        'updatedAt': Timestamp.fromDate(now),
        'likes': ['user1', 'user2'],
        'comments': ['comment1'],
        'shares': [],
        'isActive': true,
      };

      final post = PostModel.fromMap(map);

      expect(post.id, equals('test_post_id'));
      expect(post.userId, equals('test_user_id'));
      expect(post.username, equals('testuser'));
      expect(post.userDisplayName, equals('Test User'));
      expect(post.content, equals('Test content'));
      expect(post.imageUrls, equals(['https://example.com/image1.jpg']));
      expect(post.likeCount, equals(2));
      expect(post.commentCount, equals(1));
      expect(post.shareCount, equals(0));
      expect(post.hasImages, isTrue);
      expect(post.isActive, isTrue);
    });

    test('should check if user liked post correctly', () {
      final post = PostModel(
        id: 'test_post_id',
        userId: 'test_user_id',
        username: 'testuser',
        userDisplayName: 'Test User',
        content: 'Test content',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        likes: ['user1', 'user2', 'user3'],
      );

      expect(post.isLikedBy('user1'), isTrue);
      expect(post.isLikedBy('user2'), isTrue);
      expect(post.isLikedBy('user4'), isFalse);
    });

    test('should generate time ago string correctly', () {
      final now = DateTime.now();
      
      // Test "Just now"
      final justNowPost = PostModel(
        id: 'test_post_id',
        userId: 'test_user_id',
        username: 'testuser',
        userDisplayName: 'Test User',
        content: 'Test content',
        createdAt: now,
        updatedAt: now,
      );
      expect(justNowPost.timeAgo, equals('Just now'));

      // Test minutes ago
      final minutesAgoPost = PostModel(
        id: 'test_post_id',
        userId: 'test_user_id',
        username: 'testuser',
        userDisplayName: 'Test User',
        content: 'Test content',
        createdAt: now.subtract(const Duration(minutes: 30)),
        updatedAt: now,
      );
      expect(minutesAgoPost.timeAgo, equals('30m ago'));

      // Test hours ago
      final hoursAgoPost = PostModel(
        id: 'test_post_id',
        userId: 'test_user_id',
        username: 'testuser',
        userDisplayName: 'Test User',
        content: 'Test content',
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now,
      );
      expect(hoursAgoPost.timeAgo, equals('2h ago'));

      // Test days ago
      final daysAgoPost = PostModel(
        id: 'test_post_id',
        userId: 'test_user_id',
        username: 'testuser',
        userDisplayName: 'Test User',
        content: 'Test content',
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now,
      );
      expect(daysAgoPost.timeAgo, equals('3d ago'));
    });
  });
}
