class AnalyticsModel {
  final int totalUsers;
  final int totalResellers;
  final int totalAdmins;
  final int totalPosts;
  final int totalProducts;
  final int totalComments;
  final int activeUsers;
  final int pendingResellerApplications;
  final int newUsersToday;
  final int newPostsToday;
  final int newProductsToday;
  final double totalRevenue;
  final Map<String, int> usersByCountry;
  final Map<String, int> postsByCategory;
  final Map<String, int> productsByCategory;
  final List<DailyStats> dailyStats;
  final DateTime lastUpdated;

  AnalyticsModel({
    required this.totalUsers,
    required this.totalResellers,
    required this.totalAdmins,
    required this.totalPosts,
    required this.totalProducts,
    required this.totalComments,
    required this.activeUsers,
    required this.pendingResellerApplications,
    required this.newUsersToday,
    required this.newPostsToday,
    required this.newProductsToday,
    required this.totalRevenue,
    required this.usersByCountry,
    required this.postsByCategory,
    required this.productsByCategory,
    required this.dailyStats,
    required this.lastUpdated,
  });

  // Create AnalyticsModel from Map
  factory AnalyticsModel.fromMap(Map<String, dynamic> map) {
    return AnalyticsModel(
      totalUsers: map['totalUsers'] ?? 0,
      totalResellers: map['totalResellers'] ?? 0,
      totalAdmins: map['totalAdmins'] ?? 0,
      totalPosts: map['totalPosts'] ?? 0,
      totalProducts: map['totalProducts'] ?? 0,
      totalComments: map['totalComments'] ?? 0,
      activeUsers: map['activeUsers'] ?? 0,
      pendingResellerApplications: map['pendingResellerApplications'] ?? 0,
      newUsersToday: map['newUsersToday'] ?? 0,
      newPostsToday: map['newPostsToday'] ?? 0,
      newProductsToday: map['newProductsToday'] ?? 0,
      totalRevenue: (map['totalRevenue'] ?? 0).toDouble(),
      usersByCountry: Map<String, int>.from(map['usersByCountry'] ?? {}),
      postsByCategory: Map<String, int>.from(map['postsByCategory'] ?? {}),
      productsByCategory: Map<String, int>.from(map['productsByCategory'] ?? {}),
      dailyStats: (map['dailyStats'] as List<dynamic>?)
          ?.map((e) => DailyStats.fromMap(e))
          .toList() ?? [],
      lastUpdated: DateTime.parse(map['lastUpdated'] ?? DateTime.now().toIso8601String()),
    );
  }

  // Convert AnalyticsModel to Map
  Map<String, dynamic> toMap() {
    return {
      'totalUsers': totalUsers,
      'totalResellers': totalResellers,
      'totalAdmins': totalAdmins,
      'totalPosts': totalPosts,
      'totalProducts': totalProducts,
      'totalComments': totalComments,
      'activeUsers': activeUsers,
      'pendingResellerApplications': pendingResellerApplications,
      'newUsersToday': newUsersToday,
      'newPostsToday': newPostsToday,
      'newProductsToday': newProductsToday,
      'totalRevenue': totalRevenue,
      'usersByCountry': usersByCountry,
      'postsByCategory': postsByCategory,
      'productsByCategory': productsByCategory,
      'dailyStats': dailyStats.map((e) => e.toMap()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  // Helper getters
  double get userGrowthRate {
    if (dailyStats.length < 2) return 0.0;
    final today = dailyStats.last.newUsers;
    final yesterday = dailyStats[dailyStats.length - 2].newUsers;
    if (yesterday == 0) return today > 0 ? 100.0 : 0.0;
    return ((today - yesterday) / yesterday) * 100;
  }

  double get postGrowthRate {
    if (dailyStats.length < 2) return 0.0;
    final today = dailyStats.last.newPosts;
    final yesterday = dailyStats[dailyStats.length - 2].newPosts;
    if (yesterday == 0) return today > 0 ? 100.0 : 0.0;
    return ((today - yesterday) / yesterday) * 100;
  }

  double get productGrowthRate {
    if (dailyStats.length < 2) return 0.0;
    final today = dailyStats.last.newProducts;
    final yesterday = dailyStats[dailyStats.length - 2].newProducts;
    if (yesterday == 0) return today > 0 ? 100.0 : 0.0;
    return ((today - yesterday) / yesterday) * 100;
  }

  String get formattedRevenue => '\$${totalRevenue.toStringAsFixed(2)}';

  @override
  String toString() {
    return 'AnalyticsModel(totalUsers: $totalUsers, totalPosts: $totalPosts, totalProducts: $totalProducts)';
  }
}

class DailyStats {
  final DateTime date;
  final int newUsers;
  final int newPosts;
  final int newProducts;
  final int newComments;
  final double revenue;

  DailyStats({
    required this.date,
    required this.newUsers,
    required this.newPosts,
    required this.newProducts,
    required this.newComments,
    required this.revenue,
  });

  // Create DailyStats from Map
  factory DailyStats.fromMap(Map<String, dynamic> map) {
    return DailyStats(
      date: DateTime.parse(map['date']),
      newUsers: map['newUsers'] ?? 0,
      newPosts: map['newPosts'] ?? 0,
      newProducts: map['newProducts'] ?? 0,
      newComments: map['newComments'] ?? 0,
      revenue: (map['revenue'] ?? 0).toDouble(),
    );
  }

  // Convert DailyStats to Map
  Map<String, dynamic> toMap() {
    return {
      'date': date.toIso8601String(),
      'newUsers': newUsers,
      'newPosts': newPosts,
      'newProducts': newProducts,
      'newComments': newComments,
      'revenue': revenue,
    };
  }

  String get formattedDate {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}';
  }

  @override
  String toString() {
    return 'DailyStats(date: $date, newUsers: $newUsers, newPosts: $newPosts, newProducts: $newProducts)';
  }
}
