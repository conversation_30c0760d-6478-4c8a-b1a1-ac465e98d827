# Profile Components - Facebook Style

এই ডিরেক্টরিতে ফেইসবুক-স্টাইল প্রোফাইল পেইজের জন্য আধুনিক এবং পুনঃব্যবহারযোগ্য কম্পোনেন্টগুলি রয়েছে।

## কম্পোনেন্ট তালিকা

### 1. ProfileHeader (`profile_header.dart`) - Facebook Style
- **কভার ফটো সেকশন**: 200px উচ্চতা, রাউন্ডেড কর্নার
- **প্রোফাইল ছবি ওভারলে**: কভার ফটোর নিচে 100px সাইজ
- **ইউজার ইনফো সেকশন**: নাম, ইউজারনেম, বায়ো, লোকেশন
- **ইন্টিগ্রেটেড ডিজাইন**: সব তথ্য একই কার্ডে

### 2. StatisticsCard (`statistics_card.dart`) - Horizontal Layout
- ফলোয়ার, ফলোয়িং এবং পোস্ট কাউন্ট
- হরাইজন্টাল লেআউট (3 কলাম)
- ব্যাকগ্রাউন্ড কার্ড সহ প্রতিটি স্ট্যাট

### 3. ActionButtons (`action_buttons.dart`) - Facebook Style
- **নিজের প্রোফাইল**: Edit Profile + Add Story + More
- **অন্যের প্রোফাইল**: Follow + Message + More
- ফুল-উইথ বাটন ডিজাইন

### 4. ProfileContainer (`profile_container.dart`) - Simplified
- <PERSON><PERSON>eader (সব ইউজার ইনফো সহ)
- Statistics + Action Buttons কন্টেইনার
- ক্লিন এবং সিম্পল লেআউট

### 5. ProfileTabs (`profile_tabs.dart`) - Card Style
- রাউন্ডেড কার্ড ডিজাইন
- প্রোডাক্ট এবং পোস্ট ট্যাব
- ইমপ্রুভড ট্যাব ইন্ডিকেটর

### 6. ProfileAppBar (`profile_app_bar.dart`)
- কাস্টম অ্যাপ বার
- গ্রেডিয়েন্ট ব্যাকগ্রাউন্ড
- শেয়ার এবং মেনু অপশন

## ব্যবহার

```dart
import '../widgets/profile/index.dart';

// প্রোফাইল কন্টেইনার ব্যবহার
ProfileContainer(
  user: user,
  postsCount: postsCount,
  isOwnProfile: true,
  onEditProfile: () => {},
  // অন্যান্য কলব্যাক...
)
```

## বৈশিষ্ট্য

- ✅ কম্পোনেন্ট-বেসড আর্কিটেকচার
- ✅ পুনঃব্যবহারযোগ্য কোড
- ✅ আধুনিক UI ডিজাইন
- ✅ রেসপন্সিভ লেআউট
- ✅ হ্যাপটিক ফিডব্যাক
- ✅ লোডিং স্টেট
- ✅ এরর হ্যান্ডলিং
- ✅ এম্পটি স্টেট
- ✅ পুল-টু-রিফ্রেশ

## ডিজাইন প্রিন্সিপাল

1. **মডুলারিটি**: প্রতিটি কম্পোনেন্ট একটি নির্দিষ্ট দায়িত্ব পালন করে
2. **পুনঃব্যবহারযোগ্যতা**: কম্পোনেন্টগুলি বিভিন্ন জায়গায় ব্যবহার করা যায়
3. **কনসিস্টেন্সি**: সব কম্পোনেন্টে একই ডিজাইন ভাষা
4. **পারফরমেন্স**: অপ্টিমাইজড রেন্ডারিং এবং মেমোরি ব্যবহার
