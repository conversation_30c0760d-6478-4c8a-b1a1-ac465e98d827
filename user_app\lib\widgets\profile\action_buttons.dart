import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_constants.dart';
import '../../models/user_model.dart';
import '../../utils/role_access_control.dart';

class ActionButtons extends StatelessWidget {
  final VoidCallback? onEditProfile;
  final VoidCallback? onLogout;
  final VoidCallback? onSettings;
  final VoidCallback? onProductDashboard;
  final bool isOwnProfile;
  final UserModel? user;

  const ActionButtons({
    super.key,
    this.onEditProfile,
    this.onLogout,
    this.onSettings,
    this.onProductDashboard,
    this.isOwnProfile = true,
    this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (isOwnProfile) ...[
          Expanded(
            child: _buildEditProfileButton(),
          ),
          const SizedBox(width: 8),
          if (RoleAccessControl.canAccessProductDashboard(user))
            Expanded(
              child: _buildProductDashboardButton(),
            ),
          if (RoleAccessControl.canAccessProductDashboard(user))
            const SizedBox(width: 8),
          const SizedBox(width: 8),
          _buildMoreButton(),
        ] else ...[
          Expanded(
            child: _buildFollowButton(),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildMessageButton(),
          ),
          const SizedBox(width: 8),
          _buildMoreButton(),
        ],
      ],
    );
  }

  Widget _buildEditProfileButton() {
    return ElevatedButton(
      onPressed: () {
        HapticFeedback.lightImpact();
        onEditProfile?.call();
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 0,
      ),
      child: const Text(
        'Edit profile',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildProductDashboardButton() {
    return ElevatedButton(
      onPressed: () {
        HapticFeedback.lightImpact();
        onProductDashboard?.call();
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppConstants.secondaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 0,
      ),
      child: const Text(
        'Products',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }



  Widget _buildFollowButton() {
    return ElevatedButton(
      onPressed: () {
        HapticFeedback.lightImpact();
        // TODO: Implement follow functionality
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 0,
      ),
      child: const Text(
        'Follow',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildMessageButton() {
    return ElevatedButton(
      onPressed: () {
        HapticFeedback.lightImpact();
        // TODO: Implement message functionality
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppConstants.backgroundColor,
        foregroundColor: AppConstants.textPrimaryColor,
        padding: const EdgeInsets.symmetric(vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 0,
      ),
      child: const Text(
        'Message',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildMoreButton() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: PopupMenuButton<String>(
        onSelected: (value) {
          HapticFeedback.lightImpact();
          switch (value) {
            case 'settings':
              onSettings?.call();
              break;
            case 'logout':
              onLogout?.call();
              break;
          }
        },
        icon: const Icon(
          Icons.more_horiz,
          color: AppConstants.textPrimaryColor,
          size: 20,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        itemBuilder: (BuildContext context) => [
          if (onSettings != null)
            const PopupMenuItem<String>(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings_outlined, size: 20),
                  SizedBox(width: 12),
                  Text('Settings'),
                ],
              ),
            ),
          if (onLogout != null)
            const PopupMenuItem<String>(
              value: 'logout',
              child: Row(
                children: [
                  Icon(Icons.logout, color: AppConstants.errorColor, size: 20),
                  SizedBox(width: 12),
                  Text('Logout'),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
