import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class NavigationItem {
  final dynamic icon;
  final dynamic activeIcon;
  final String label;
  final String tooltip;
  final int index;
  final int? badgeCount;
  final Color? badgeColor;
  final bool isDivider;
  final String? route;

  const NavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    this.tooltip = '',
    required this.index,
    this.badgeCount,
    this.badgeColor,
    this.isDivider = false,
    this.route,
  });
}

class NavigationGroup {
  final String title;
  final List<NavigationItem> items;
  final bool collapsible;
  bool isExpanded;

  NavigationGroup({
    required this.title,
    required this.items,
    this.collapsible = false,
    this.isExpanded = true,
  });
}

class NavigationItemTile extends StatelessWidget {
  final NavigationItem item;
  final bool isSelected;
  final bool isExpanded;
  final bool isHovered;
  final VoidCallback onTap;
  final Animation<double>? animation;

  const NavigationItemTile({
    required this.item,
    required this.isSelected,
    required this.isExpanded,
    this.isHovered = false,
    required this.onTap,
    this.animation,
  });

  @override
  Widget build(BuildContext context) {
    if (item.isDivider) {
      return const Divider(height: 1, thickness: 1);
    }

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    final effectiveTooltip = item.tooltip.isEmpty ? item.label : item.tooltip;
    final content = Material(
      color: isSelected 
          ? colorScheme.primary.withOpacity(0.1)
          : isHovered 
              ? theme.hoverColor
              : Colors.transparent,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        hoverColor: colorScheme.primary.withOpacity(0.08),
        highlightColor: colorScheme.primary.withOpacity(0.15),
        splashColor: colorScheme.primary.withOpacity(0.2),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          child: Row(
            children: [
              // Icon with badge
              Stack(
                clipBehavior: Clip.none,
                children: [
                  // Animated icon
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 200),
                    transitionBuilder: (child, animation) {
                      return ScaleTransition(
                        scale: animation,
                        child: child,
                      );
                    },
                    child: Icon(
                      isSelected ? item.activeIcon : item.icon,
                      key: ValueKey<bool>(isSelected),
                      color: isSelected 
                          ? colorScheme.primary 
                          : theme.textTheme.bodyMedium?.color?.withOpacity(0.8),
                      size: 22,
                    ),
                  ),
                  
                  // Badge
                  if (item.badgeCount != null && item.badgeCount! > 0)
                    Positioned(
                      right: -8,
                      top: -6,
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: item.badgeColor ?? colorScheme.error,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            if (!isDark)
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 3,
                                offset: const Offset(0, 1),
                              ),
                          ],
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 20,
                          minHeight: 20,
                        ),
                        child: Center(
                          child: Text(
                            item.badgeCount! > 9 ? '9+' : '${item.badgeCount!}',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              height: 1,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              
              // Label with animation
              if (isExpanded) ...[
                const SizedBox(width: 16),
                Expanded(
                  child: AnimatedOpacity(
                    opacity: isExpanded ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 150),
                    child: Text(
                      item.label,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: isSelected 
                            ? colorScheme.primary 
                            : theme.textTheme.bodyMedium?.color?.withOpacity(0.9),
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        letterSpacing: 0.2,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );

    // If not expanded, wrap with tooltip
    if (!isExpanded) {
      return Tooltip(
        message: effectiveTooltip,
        preferBelow: false,
        verticalOffset: 8,
        child: content,
      );
    }

    return content;
  }
}
