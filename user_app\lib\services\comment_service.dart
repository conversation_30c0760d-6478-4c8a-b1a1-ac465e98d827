import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../models/comment_model.dart';
import '../models/user_model.dart';
import 'post_service.dart';

class CommentService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'comments';
  static const Uuid _uuid = Uuid();

  /// Create a new comment
  static Future<CommentModel?> createComment({
    required String postId,
    required UserModel user,
    required String content,
  }) async {
    try {
      // Generate unique comment ID
      final commentId = _uuid.v4();
      final now = DateTime.now();

      // Create comment model
      final comment = CommentModel(
        id: commentId,
        postId: postId,
        userId: user.id,
        username: user.username,
        userDisplayName: user.displayName,
        userProfileImageUrl: user.profileImageUrl,
        content: content,
        createdAt: now,
        updatedAt: now,
      );

      // Save comment to Firestore
      await _firestore
          .collection(_collection)
          .doc(commentId)
          .set(comment.toMap());

      // Update post's comment count
      await PostService.addComment(
        postId: postId,
        commentId: commentId,
      );

      return comment;
    } catch (e) {
      print('Error creating comment: $e');
      return null;
    }
  }

  /// Get comments for a post
  static Future<List<CommentModel>> getCommentsByPost({
    required String postId,
    int limit = 50,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = _firestore
          .collection(_collection)
          .where('postId', isEqualTo: postId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: false)
          .limit(limit);

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final querySnapshot = await query.get();
      
      return querySnapshot.docs
          .map((doc) => CommentModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting comments: $e');
      return [];
    }
  }

  /// Get a single comment by ID
  static Future<CommentModel?> getCommentById(String commentId) async {
    try {
      final doc = await _firestore
          .collection(_collection)
          .doc(commentId)
          .get();

      if (doc.exists) {
        return CommentModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      print('Error getting comment: $e');
      return null;
    }
  }

  /// Update comment content
  static Future<bool> updateComment({
    required String commentId,
    required String content,
  }) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(commentId)
          .update({
        'content': content,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      return true;
    } catch (e) {
      print('Error updating comment: $e');
      return false;
    }
  }

  /// Delete comment (soft delete)
  static Future<bool> deleteComment({
    required String commentId,
    required String postId,
  }) async {
    try {
      // Soft delete comment
      await _firestore
          .collection(_collection)
          .doc(commentId)
          .update({
        'isActive': false,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      // Remove comment from post
      await PostService.removeComment(
        postId: postId,
        commentId: commentId,
      );

      return true;
    } catch (e) {
      print('Error deleting comment: $e');
      return false;
    }
  }

  /// Like/Unlike comment
  static Future<bool> toggleLike({
    required String commentId,
    required String userId,
  }) async {
    try {
      final commentRef = _firestore.collection(_collection).doc(commentId);
      
      return await _firestore.runTransaction((transaction) async {
        final commentDoc = await transaction.get(commentRef);
        
        if (!commentDoc.exists) {
          throw Exception('Comment not found');
        }

        final comment = CommentModel.fromDocument(commentDoc);
        List<String> likes = List.from(comment.likes);

        if (likes.contains(userId)) {
          // Unlike
          likes.remove(userId);
        } else {
          // Like
          likes.add(userId);
        }

        transaction.update(commentRef, {
          'likes': likes,
          'updatedAt': Timestamp.fromDate(DateTime.now()),
        });

        return true;
      });
    } catch (e) {
      print('Error toggling comment like: $e');
      return false;
    }
  }

  /// Get comments stream for real-time updates
  static Stream<List<CommentModel>> getCommentsStream({
    required String postId,
    int limit = 50,
  }) {
    return _firestore
        .collection(_collection)
        .where('postId', isEqualTo: postId)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: false)
        .limit(limit)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => CommentModel.fromDocument(doc))
            .toList());
  }

  /// Get comment count for a post
  static Future<int> getCommentCount(String postId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('postId', isEqualTo: postId)
          .where('isActive', isEqualTo: true)
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      print('Error getting comment count: $e');
      return 0;
    }
  }

  /// Get comments by user
  static Future<List<CommentModel>> getCommentsByUser({
    required String userId,
    int limit = 20,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final querySnapshot = await query.get();
      
      return querySnapshot.docs
          .map((doc) => CommentModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting user comments: $e');
      return [];
    }
  }
}
