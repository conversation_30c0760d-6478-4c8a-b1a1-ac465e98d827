import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/support_model.dart';

class SupportService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'support_requests';

  // Get all support requests with pagination
  static Future<List<SupportModel>> getSupportRequests({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    SupportStatus? statusFilter,
    String? categoryFilter,
  }) async {
    try {
      Query query = _firestore.collection(_collection);

      // Apply filters
      if (statusFilter != null) {
        query = query.where('status', isEqualTo: statusFilter.name);
      }

      if (categoryFilter != null && categoryFilter != 'all') {
        query = query.where('category', isEqualTo: categoryFilter);
      }

      // Order by creation date (newest first)
      query = query.orderBy('createdAt', descending: true);

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => SupportModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error getting support requests: $e');
      return [];
    }
  }

  // Get support request by ID
  static Future<SupportModel?> getSupportRequestById(String id) async {
    try {
      final doc = await _firestore.collection(_collection).doc(id).get();
      if (doc.exists) {
        return SupportModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      print('Error getting support request: $e');
      return null;
    }
  }

  // Update support request status
  static Future<bool> updateSupportStatus({
    required String id,
    required SupportStatus status,
    String? adminNote,
    String? assignedAdminId,
    String? assignedAdminName,
  }) async {
    try {
      final updateData = {
        'status': status.name,
        'updatedAt': Timestamp.now(),
      };

      if (adminNote != null) {
        updateData['adminNote'] = adminNote;
      }

      if (assignedAdminId != null) {
        updateData['assignedAdminId'] = assignedAdminId;
      }

      if (assignedAdminName != null) {
        updateData['assignedAdminName'] = assignedAdminName;
      }

      await _firestore.collection(_collection).doc(id).update(updateData);
      return true;
    } catch (e) {
      print('Error updating support status: $e');
      return false;
    }
  }

  // Add admin note to support request
  static Future<bool> addAdminNote({
    required String id,
    required String note,
    String? adminId,
    String? adminName,
  }) async {
    try {
      final updateData = {
        'adminNote': note,
        'updatedAt': Timestamp.now(),
      };

      if (adminId != null) {
        updateData['assignedAdminId'] = adminId;
      }

      if (adminName != null) {
        updateData['assignedAdminName'] = adminName;
      }

      await _firestore.collection(_collection).doc(id).update(updateData);
      return true;
    } catch (e) {
      print('Error adding admin note: $e');
      return false;
    }
  }

  // Get support statistics
  static Future<Map<String, int>> getSupportStatistics() async {
    try {
      final snapshot = await _firestore.collection(_collection).get();
      
      final stats = {
        'total': 0,
        'pending': 0,
        'inProgress': 0,
        'resolved': 0,
        'closed': 0,
      };

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final status = data['status'] as String?;
        
        stats['total'] = stats['total']! + 1;
        
        switch (status) {
          case 'pending':
            stats['pending'] = stats['pending']! + 1;
            break;
          case 'inProgress':
            stats['inProgress'] = stats['inProgress']! + 1;
            break;
          case 'resolved':
            stats['resolved'] = stats['resolved']! + 1;
            break;
          case 'closed':
            stats['closed'] = stats['closed']! + 1;
            break;
        }
      }

      return stats;
    } catch (e) {
      print('Error getting support statistics: $e');
      return {
        'total': 0,
        'pending': 0,
        'inProgress': 0,
        'resolved': 0,
        'closed': 0,
      };
    }
  }

  // Get available categories
  static Future<List<String>> getCategories() async {
    try {
      final snapshot = await _firestore.collection(_collection).get();
      final categories = <String>{};
      
      for (final doc in snapshot.docs) {
        final data = doc.data();
        final category = data['category'] as String?;
        if (category != null && category.isNotEmpty) {
          categories.add(category);
        }
      }
      
      return categories.toList()..sort();
    } catch (e) {
      print('Error getting categories: $e');
      return ['General', 'Technical Issue', 'Account Problem', 'Payment Issue', 'Bug Report', 'Feature Request'];
    }
  }

  // Delete support request
  static Future<bool> deleteSupportRequest(String id) async {
    try {
      await _firestore.collection(_collection).doc(id).delete();
      return true;
    } catch (e) {
      print('Error deleting support request: $e');
      return false;
    }
  }

  // Bulk update support requests
  static Future<bool> bulkUpdateStatus({
    required List<String> ids,
    required SupportStatus status,
    String? adminNote,
    String? assignedAdminId,
    String? assignedAdminName,
  }) async {
    try {
      final batch = _firestore.batch();
      
      for (final id in ids) {
        final docRef = _firestore.collection(_collection).doc(id);
        final updateData = {
          'status': status.name,
          'updatedAt': Timestamp.now(),
        };

        if (adminNote != null) {
          updateData['adminNote'] = adminNote;
        }

        if (assignedAdminId != null) {
          updateData['assignedAdminId'] = assignedAdminId;
        }

        if (assignedAdminName != null) {
          updateData['assignedAdminName'] = assignedAdminName;
        }

        batch.update(docRef, updateData);
      }
      
      await batch.commit();
      return true;
    } catch (e) {
      print('Error bulk updating support requests: $e');
      return false;
    }
  }
}
