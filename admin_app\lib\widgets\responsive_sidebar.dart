import 'package:flutter/material.dart';
import '../models/navigation_models.dart';

class ResponsiveSidebar extends StatefulWidget {
  final List<NavigationGroup> navigationGroups;
  final int selectedIndex;
  final ValueChanged<int> onItemSelected;
  final Widget Function(ThemeData) buildHeader;
  final Widget Function(ThemeData) buildFooter;
  final double sidebarWidth;
  final double collapsedWidth;
  final Duration animationDuration;

  const ResponsiveSidebar({
    Key? key,
    required this.navigationGroups,
    required this.selectedIndex,
    required this.onItemSelected,
    required this.buildHeader,
    required this.buildFooter,
    this.sidebarWidth = 260,
    this.collapsedWidth = 72,
    this.animationDuration = const Duration(milliseconds: 300),
  }) : super(key: key);

  @override
  _ResponsiveSidebarState createState() => _ResponsiveSidebarState();
}

class _ResponsiveSidebarState extends State<ResponsiveSidebar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _widthAnimation;
  bool _isExpanded = false; // Changed to false - sidebar closed by default
  bool _isMobile = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    _widthAnimation = Tween<double>(
      begin: widget.sidebarWidth,
      end: widget.sidebarWidth,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final newIsMobile = MediaQuery.of(context).size.width < 768;
    if (newIsMobile != _isMobile) {
      setState(() {
        _isMobile = newIsMobile;
        // Always start with sidebar closed by default
        _isExpanded = false;
      });
    }
  }

  void _toggleSidebar() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  void _onItemTapped(int index) {
    widget.onItemSelected(index);
    if (_isMobile) {
      _toggleSidebar();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    final sidebar = AnimatedContainer(
      duration: widget.animationDuration,
      width: _isExpanded ? widget.sidebarWidth : widget.collapsedWidth,
      color: isDark ? theme.colorScheme.surface : Colors.white,
      child: Column(
        children: [
          // Header
          widget.buildHeader(theme),
          
          // Navigation Menu
          Expanded(
            child: ListView.builder(
              itemCount: widget.navigationGroups.length,
              itemBuilder: (context, groupIndex) {
                final group = widget.navigationGroups[groupIndex];
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (group.title.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                        child: Text(
                          group.title,
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.textTheme.bodySmall?.color,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 1.0,
                          ),
                        ),
                      ),
                    ...group.items.map((item) {
                      final isSelected = widget.selectedIndex == item.index;
                      return ListTile(
                        leading: Icon(
                          isSelected ? item.activeIcon : item.icon,
                          color: isSelected
                              ? theme.colorScheme.primary
                              : theme.iconTheme.color?.withOpacity(0.7),
                        ),
                        title: _isExpanded
                            ? Text(
                                item.label,
                                style: theme.textTheme.titleSmall?.copyWith(
                                  color: isSelected
                                      ? theme.colorScheme.primary
                                      : null,
                                  fontWeight:
                                      isSelected ? FontWeight.w600 : null,
                                ),
                              )
                            : null,
                        onTap: () => _onItemTapped(item.index),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        dense: true,
                      );
                    }).toList(),
                    if (groupIndex < widget.navigationGroups.length - 1)
                      const Divider(height: 8, thickness: 1),
                  ],
                );
              },
            ),
          ),
          
          // Footer
          widget.buildFooter(theme),
        ],
      ),
    );

    if (!_isMobile) {
      return MouseRegion(
        onEnter: (_) {
          if (!_isExpanded) {
            _toggleSidebar();
          }
        },
        onExit: (_) {
          if (_isExpanded) {
            _toggleSidebar();
          }
        },
        child: sidebar,
      );
    }

    return Stack(
      children: [
        // Sidebar overlay
        if (_isExpanded)
          GestureDetector(
            onTap: _toggleSidebar,
            child: Container(
              color: Colors.black54,
            ),
          ),
        // Sidebar
        AnimatedPositioned(
          duration: widget.animationDuration,
          left: _isExpanded ? 0 : -widget.sidebarWidth,
          top: 0,
          bottom: 0,
          width: widget.sidebarWidth,
          child: Material(
            elevation: 8.0,
            child: sidebar,
          ),
        ),
        // Floating toggle button
        if (!_isExpanded)
          Positioned(
            left: 16,
            bottom: 16,
            child: FloatingActionButton(
              heroTag: 'menu_toggle',
              onPressed: _toggleSidebar,
              child: const Icon(Icons.menu),
              backgroundColor: theme.primaryColor,
            ),
          ),
      ],
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}
