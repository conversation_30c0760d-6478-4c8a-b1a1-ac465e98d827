import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/post_model.dart';
import '../models/product_model.dart';
import '../models/comment_model.dart';
import '../constants/app_constants.dart';

class ContentManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // ========== POST MANAGEMENT ==========

  // Get all posts with filters and pagination
  static Future<List<PostModel>> getPosts({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    bool? isActiveFilter,
    String? userIdFilter,
    String? searchQuery,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.postsCollection)
          .orderBy('createdAt', descending: true);

      // Apply filters
      if (isActiveFilter != null) {
        query = query.where('isActive', isEqualTo: isActiveFilter);
      }
      if (userIdFilter != null) {
        query = query.where('userId', isEqualTo: userIdFilter);
      }

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final QuerySnapshot querySnapshot = await query.get();
      List<PostModel> posts = querySnapshot.docs
          .map((doc) => PostModel.fromDocument(doc))
          .toList();

      // Apply search filter locally if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        posts = posts.where((post) {
          return post.content.toLowerCase().contains(searchLower) ||
                 post.username.toLowerCase().contains(searchLower) ||
                 post.userDisplayName.toLowerCase().contains(searchLower);
        }).toList();
      }

      return posts;
    } catch (e) {
      throw Exception('Failed to get posts: ${e.toString()}');
    }
  }

  // Get post by ID
  static Future<PostModel?> getPostById(String postId) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection(AppConstants.postsCollection)
          .doc(postId)
          .get();

      if (doc.exists) {
        return PostModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get post: ${e.toString()}');
    }
  }

  // Toggle post active status
  static Future<void> togglePostActiveStatus(String postId, bool isActive) async {
    try {
      await _firestore
          .collection(AppConstants.postsCollection)
          .doc(postId)
          .update({
        'isActive': isActive,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to update post status: ${e.toString()}');
    }
  }

  // Delete post (soft delete)
  static Future<void> deletePost(String postId) async {
    try {
      await _firestore
          .collection(AppConstants.postsCollection)
          .doc(postId)
          .update({
        'isActive': false,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to delete post: ${e.toString()}');
    }
  }

  // ========== PRODUCT MANAGEMENT ==========

  // Get all products with filters and pagination
  static Future<List<ProductModel>> getProducts({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    bool? isAvailableFilter,
    bool? isFeaturedFilter,
    String? categoryFilter,
    String? sellerIdFilter,
    String? searchQuery,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.productsCollection)
          .orderBy('createdAt', descending: true);

      // Apply filters
      if (isAvailableFilter != null) {
        query = query.where('isAvailable', isEqualTo: isAvailableFilter);
      }
      if (isFeaturedFilter != null) {
        query = query.where('isFeatured', isEqualTo: isFeaturedFilter);
      }
      if (categoryFilter != null && categoryFilter.isNotEmpty) {
        query = query.where('category', isEqualTo: categoryFilter);
      }
      if (sellerIdFilter != null) {
        query = query.where('sellerId', isEqualTo: sellerIdFilter);
      }

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final QuerySnapshot querySnapshot = await query.get();
      List<ProductModel> products = querySnapshot.docs
          .map((doc) => ProductModel.fromDocument(doc))
          .toList();

      // Apply search filter locally if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        products = products.where((product) {
          return product.name.toLowerCase().contains(searchLower) ||
                 product.description.toLowerCase().contains(searchLower) ||
                 product.category.toLowerCase().contains(searchLower) ||
                 product.sellerName.toLowerCase().contains(searchLower);
        }).toList();
      }

      return products;
    } catch (e) {
      throw Exception('Failed to get products: ${e.toString()}');
    }
  }

  // Get product by ID
  static Future<ProductModel?> getProductById(String productId) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection(AppConstants.productsCollection)
          .doc(productId)
          .get();

      if (doc.exists) {
        return ProductModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get product: ${e.toString()}');
    }
  }

  // Toggle product availability
  static Future<void> toggleProductAvailability(String productId, bool isAvailable) async {
    try {
      await _firestore
          .collection(AppConstants.productsCollection)
          .doc(productId)
          .update({
        'isAvailable': isAvailable,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to update product availability: ${e.toString()}');
    }
  }

  // Toggle product featured status
  static Future<void> toggleProductFeaturedStatus(String productId, bool isFeatured) async {
    try {
      await _firestore
          .collection(AppConstants.productsCollection)
          .doc(productId)
          .update({
        'isFeatured': isFeatured,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to update product featured status: ${e.toString()}');
    }
  }

  // Delete product (soft delete)
  static Future<void> deleteProduct(String productId) async {
    try {
      await _firestore
          .collection(AppConstants.productsCollection)
          .doc(productId)
          .update({
        'isAvailable': false,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to delete product: ${e.toString()}');
    }
  }

  // Get product categories
  static Future<List<String>> getProductCategories() async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.productsCollection)
          .get();

      final Set<String> categories = {};
      for (final doc in querySnapshot.docs) {
        final product = ProductModel.fromDocument(doc);
        categories.add(product.category);
      }

      return categories.toList()..sort();
    } catch (e) {
      throw Exception('Failed to get categories: ${e.toString()}');
    }
  }

  // ========== COMMENT MANAGEMENT ==========

  // Get all comments with filters and pagination
  static Future<List<CommentModel>> getComments({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    bool? isActiveFilter,
    String? postIdFilter,
    String? userIdFilter,
    String? searchQuery,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.commentsCollection)
          .orderBy('createdAt', descending: true);

      // Apply filters
      if (isActiveFilter != null) {
        query = query.where('isActive', isEqualTo: isActiveFilter);
      }
      if (postIdFilter != null) {
        query = query.where('postId', isEqualTo: postIdFilter);
      }
      if (userIdFilter != null) {
        query = query.where('userId', isEqualTo: userIdFilter);
      }

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final QuerySnapshot querySnapshot = await query.get();
      List<CommentModel> comments = querySnapshot.docs
          .map((doc) => CommentModel.fromDocument(doc))
          .toList();

      // Apply search filter locally if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        comments = comments.where((comment) {
          return comment.content.toLowerCase().contains(searchLower) ||
                 comment.username.toLowerCase().contains(searchLower) ||
                 comment.userDisplayName.toLowerCase().contains(searchLower);
        }).toList();
      }

      return comments;
    } catch (e) {
      throw Exception('Failed to get comments: ${e.toString()}');
    }
  }

  // Get comment by ID
  static Future<CommentModel?> getCommentById(String commentId) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection(AppConstants.commentsCollection)
          .doc(commentId)
          .get();

      if (doc.exists) {
        return CommentModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get comment: ${e.toString()}');
    }
  }

  // Toggle comment active status
  static Future<void> toggleCommentActiveStatus(String commentId, bool isActive) async {
    try {
      await _firestore
          .collection(AppConstants.commentsCollection)
          .doc(commentId)
          .update({
        'isActive': isActive,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to update comment status: ${e.toString()}');
    }
  }

  // Delete comment (soft delete)
  static Future<void> deleteComment(String commentId) async {
    try {
      await _firestore
          .collection(AppConstants.commentsCollection)
          .doc(commentId)
          .update({
        'isActive': false,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to delete comment: ${e.toString()}');
    }
  }

  // ========== CONTENT STATISTICS ==========

  // Get content statistics
  static Future<Map<String, int>> getContentStatistics() async {
    try {
      final futures = await Future.wait([
        _getActivePostCount(),
        _getActiveProductCount(),
        _getActiveCommentCount(),
        _getFeaturedProductCount(),
      ]);

      return {
        'activePosts': futures[0],
        'activeProducts': futures[1],
        'activeComments': futures[2],
        'featuredProducts': futures[3],
      };
    } catch (e) {
      throw Exception('Failed to get content statistics: ${e.toString()}');
    }
  }

  // Helper methods for statistics
  static Future<int> _getActivePostCount() async {
    final QuerySnapshot snapshot = await _firestore
        .collection(AppConstants.postsCollection)
        .where('isActive', isEqualTo: true)
        .get();
    return snapshot.docs.length;
  }

  static Future<int> _getActiveProductCount() async {
    final QuerySnapshot snapshot = await _firestore
        .collection(AppConstants.productsCollection)
        .where('isAvailable', isEqualTo: true)
        .get();
    return snapshot.docs.length;
  }

  static Future<int> _getActiveCommentCount() async {
    final QuerySnapshot snapshot = await _firestore
        .collection(AppConstants.commentsCollection)
        .where('isActive', isEqualTo: true)
        .get();
    return snapshot.docs.length;
  }

  static Future<int> _getFeaturedProductCount() async {
    final QuerySnapshot snapshot = await _firestore
        .collection(AppConstants.productsCollection)
        .where('isFeatured', isEqualTo: true)
        .where('isAvailable', isEqualTo: true)
        .get();
    return snapshot.docs.length;
  }
}
