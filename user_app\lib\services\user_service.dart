import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';
import '../enums/user_role.dart';
import 'auth_service.dart';

class UserService {
  static final AuthService _authService = AuthService();
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get user by ID
  static Future<UserModel?> getUserById(String userId) async {
    return await _authService.getUserById(userId);
  }

  // Search users
  static Future<List<UserModel>> searchUsers(
    String query, {
    String? excludeUserId,
    int limit = 20,
  }) async {
    final users = await _authService.searchUsers(query: query, limit: limit);
    if (excludeUserId != null) {
      return users.where((user) => user.id != excludeUserId).toList();
    }
    return users;
  }

  // Follow/Unfollow user
  static Future<void> toggleFollow({
    required String currentUserId,
    required String targetUserId,
  }) async {
    return await _authService.toggleFollowUser(
      currentUserId: currentUserId,
      targetUserId: targetUserId,
    );
  }

  // Get all resellers from database
  static Future<List<UserModel>> getResellers() async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('role', isEqualTo: UserRole.reseller.value)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch resellers: ${e.toString()}');
    }
  }

  // Search resellers
  static Future<List<UserModel>> searchResellers(String query) async {
    try {
      if (query.isEmpty) {
        return await getResellers();
      }

      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('role', isEqualTo: UserRole.reseller.value)
          .where('isActive', isEqualTo: true)
          .get();

      final List<UserModel> allResellers = querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();

      // Filter by search query (case insensitive)
      return allResellers.where((reseller) {
        final searchLower = query.toLowerCase();
        return reseller.displayName.toLowerCase().contains(searchLower) ||
               reseller.username.toLowerCase().contains(searchLower) ||
               (reseller.bio?.toLowerCase().contains(searchLower) ?? false) ||
               (reseller.address?.toLowerCase().contains(searchLower) ?? false);
      }).toList();
    } catch (e) {
      throw Exception('Failed to search resellers: ${e.toString()}');
    }
  }

  // Get reseller statistics
  static Future<Map<String, int>> getResellerStats() async {
    try {
      final QuerySnapshot allResellers = await _firestore
          .collection(AppConstants.usersCollection)
          .where('role', isEqualTo: UserRole.reseller.value)
          .get();

      final QuerySnapshot verifiedResellers = await _firestore
          .collection(AppConstants.usersCollection)
          .where('role', isEqualTo: UserRole.reseller.value)
          .where('isVerified', isEqualTo: true)
          .get();

      return {
        'total': allResellers.docs.length,
        'verified': verifiedResellers.docs.length,
      };
    } catch (e) {
      throw Exception('Failed to fetch reseller stats: ${e.toString()}');
    }
  }
}
