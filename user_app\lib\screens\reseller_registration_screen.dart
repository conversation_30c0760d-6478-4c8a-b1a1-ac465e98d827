import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../constants/app_constants.dart';
import '../providers/auth_provider.dart';
import '../services/image_service.dart';
import '../services/auth_service.dart';
import '../services/cloudinary_service.dart';
import '../widgets/common/custom_text_field.dart';
import '../widgets/common/custom_button.dart';

class ResellerRegistrationScreen extends StatefulWidget {
  const ResellerRegistrationScreen({super.key});

  @override
  State<ResellerRegistrationScreen> createState() => _ResellerRegistrationScreenState();
}

class _ResellerRegistrationScreenState extends State<ResellerRegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();
  
  // Form controllers
  final _nameController = TextEditingController();
  final _referIdController = TextEditingController();
  final _numberController = TextEditingController();
  final _addressController = TextEditingController();
  final _socialMediaController = TextEditingController();
  
  // Referrer form controllers
  final _referrerNameController = TextEditingController();
  final _referrerNumberController = TextEditingController();
  final _referrerAddressController = TextEditingController();
  
  // File uploads
  File? _nationalIdFile;
  File? _tradeLicenseFile;
  File? _referrerNationalIdFile;
  
  bool _isLoading = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void dispose() {
    _nameController.dispose();
    _referIdController.dispose();
    _numberController.dispose();
    _addressController.dispose();
    _socialMediaController.dispose();
    _referrerNameController.dispose();
    _referrerNumberController.dispose();
    _referrerAddressController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Reseller Registration',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppConstants.primaryColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          controller: _scrollController,
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionTitle('Personal Information'),
              const SizedBox(height: AppConstants.paddingMedium),
              _buildPersonalInfoSection(),
              
              const SizedBox(height: AppConstants.paddingLarge),
              _buildSectionTitle('Document Uploads'),
              const SizedBox(height: AppConstants.paddingMedium),
              _buildDocumentSection(),
              
              const SizedBox(height: AppConstants.paddingLarge),
              _buildSectionTitle('Referrer Information'),
              const SizedBox(height: AppConstants.paddingMedium),
              _buildReferrerSection(),
              
              const SizedBox(height: AppConstants.paddingExtraLarge),
              _buildSubmitButton(),
              const SizedBox(height: AppConstants.paddingLarge),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: AppConstants.primaryColor,
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          children: [
            CustomTextField(
              controller: _nameController,
              labelText: 'Full Name *',
              hintText: 'Enter your full name',
              prefixIcon: Icons.person,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter your full name';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            CustomTextField(
              controller: _referIdController,
              labelText: 'Refer ID',
              hintText: 'Enter refer ID (optional)',
              prefixIcon: Icons.badge,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            CustomTextField(
              controller: _numberController,
              labelText: 'Phone Number *',
              hintText: 'Enter your phone number',
              prefixIcon: Icons.phone,
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter your phone number';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            CustomTextField(
              controller: _addressController,
              labelText: 'Address *',
              hintText: 'Enter your complete address',
              prefixIcon: Icons.location_on,
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter your address';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            CustomTextField(
              controller: _socialMediaController,
              labelText: 'Social Media Link',
              hintText: 'Enter your social media profile link (optional)',
              prefixIcon: Icons.link,
              keyboardType: TextInputType.url,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          children: [
            _buildFileUploadField(
              title: 'National ID *',
              subtitle: 'Upload a clear photo of your National ID',
              file: _nationalIdFile,
              onTap: () => _pickFile('national_id'),
              icon: Icons.credit_card,
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildFileUploadField(
              title: 'Trade License *',
              subtitle: 'Upload your trade license document',
              file: _tradeLicenseFile,
              onTap: () => _pickFile('trade_license'),
              icon: Icons.business,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReferrerSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Referrer Details (Optional)',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppConstants.textColor,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            CustomTextField(
              controller: _referrerNameController,
              labelText: 'Referrer Name',
              hintText: 'Enter referrer\'s full name',
              prefixIcon: Icons.person_outline,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            CustomTextField(
              controller: _referrerNumberController,
              labelText: 'Referrer Phone Number',
              hintText: 'Enter referrer\'s phone number',
              prefixIcon: Icons.phone_outlined,
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            CustomTextField(
              controller: _referrerAddressController,
              labelText: 'Referrer Address',
              hintText: 'Enter referrer\'s address',
              prefixIcon: Icons.location_on_outlined,
              maxLines: 2,
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildFileUploadField(
              title: 'Referrer National ID',
              subtitle: 'Upload referrer\'s National ID (optional)',
              file: _referrerNationalIdFile,
              onTap: () => _pickFile('referrer_national_id'),
              icon: Icons.credit_card_outlined,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileUploadField({
    required String title,
    required String subtitle,
    required File? file,
    required VoidCallback onTap,
    required IconData icon,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          border: Border.all(
            color: file != null ? AppConstants.primaryColor : Colors.grey.shade300,
            width: 1.5,
          ),
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          color: file != null ? AppConstants.primaryColor.withOpacity(0.05) : Colors.grey.shade50,
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: file != null ? AppConstants.primaryColor : Colors.grey.shade400,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                file != null ? Icons.check : icon,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: file != null ? AppConstants.primaryColor : AppConstants.textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    file != null ? 'File selected: ${file.path.split('/').last}' : subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.upload_file,
              color: file != null ? AppConstants.primaryColor : Colors.grey.shade400,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        text: 'Submit Application',
        onPressed: _isLoading ? null : _submitApplication,
        isLoading: _isLoading,
        backgroundColor: AppConstants.primaryColor,
      ),
    );
  }

  Future<void> _pickFile(String type) async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );
      
      if (pickedFile != null) {
        setState(() {
          switch (type) {
            case 'national_id':
              _nationalIdFile = File(pickedFile.path);
              break;
            case 'trade_license':
              _tradeLicenseFile = File(pickedFile.path);
              break;
            case 'referrer_national_id':
              _referrerNationalIdFile = File(pickedFile.path);
              break;
          }
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error picking file: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _submitApplication() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Check required files
    if (_nationalIdFile == null || _tradeLicenseFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please upload both National ID and Trade License'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('User not found. Please login again.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Upload required documents
      final nationalIdUrl = await CloudinaryService.uploadImage(
        imageFile: _nationalIdFile!,
        folder: 'reseller_documents',
        publicId: 'national_id_${DateTime.now().millisecondsSinceEpoch}',
      );
      final tradeLicenseUrl = await CloudinaryService.uploadImage(
        imageFile: _tradeLicenseFile!,
        folder: 'reseller_documents',
        publicId: 'trade_license_${DateTime.now().millisecondsSinceEpoch}',
      );

      // Check if required uploads were successful
      if (nationalIdUrl == null || tradeLicenseUrl == null) {
        throw Exception('Failed to upload required documents. Please try again.');
      }

      String? referrerNationalIdUrl;
      if (_referrerNationalIdFile != null) {
        referrerNationalIdUrl = await CloudinaryService.uploadImage(
          imageFile: _referrerNationalIdFile!,
          folder: 'reseller_documents',
          publicId: 'referrer_national_id_${DateTime.now().millisecondsSinceEpoch}',
        );
      }

      // Submit enhanced reseller application
      final success = await AuthService().submitEnhancedResellerApplication(
        userId: currentUser.id,
        resellerName: _nameController.text.trim(),
        resellerNumber: _numberController.text.trim(),
        resellerAddress: _addressController.text.trim(),
        nationalIdUrl: nationalIdUrl,
        tradeLicenseUrl: tradeLicenseUrl,
        resellerReferId: _referIdController.text.trim().isNotEmpty
            ? _referIdController.text.trim()
            : null,
        socialMediaLink: _socialMediaController.text.trim().isNotEmpty
            ? _socialMediaController.text.trim()
            : null,
        referrerName: _referrerNameController.text.trim().isNotEmpty
            ? _referrerNameController.text.trim()
            : null,
        referrerNumber: _referrerNumberController.text.trim().isNotEmpty
            ? _referrerNumberController.text.trim()
            : null,
        referrerAddress: _referrerAddressController.text.trim().isNotEmpty
            ? _referrerAddressController.text.trim()
            : null,
        referrerNationalIdUrl: referrerNationalIdUrl,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Application submitted successfully! We will review your application and get back to you soon.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 4),
          ),
        );

        // Auto-reload the page after successful submission
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting application: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
