import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../enums/user_role.dart';

class ResellerStatusMessage extends StatefulWidget {
  final ResellerApplicationStatus status;
  final String? messageKey; // Unique key for this message to track dismissal

  const ResellerStatusMessage({
    super.key,
    required this.status,
    this.messageKey,
  });

  @override
  State<ResellerStatusMessage> createState() => _ResellerStatusMessageState();
}

class _ResellerStatusMessageState extends State<ResellerStatusMessage> {
  bool _isDismissed = false;

  @override
  void initState() {
    super.initState();
    _checkDismissalStatus();
  }

  Future<void> _checkDismissalStatus() async {
    if (widget.messageKey != null) {
      final prefs = await SharedPreferences.getInstance();
      final isDismissed = prefs.getBool('dismissed_${widget.messageKey}') ?? false;
      if (mounted) {
        setState(() {
          _isDismissed = isDismissed;
        });
      }
    }
  }

  Future<void> _dismissMessage() async {
    if (widget.messageKey != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('dismissed_${widget.messageKey}', true);
    }
    if (mounted) {
      setState(() {
        _isDismissed = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't show if dismissed or status is none
    if (_isDismissed || widget.status.isNone) {
      return const SizedBox.shrink();
    }

    Color backgroundColor;
    Color textColor;
    Color iconColor;
    IconData icon;
    String title;
    String message;

    switch (widget.status) {
      case ResellerApplicationStatus.pending:
        backgroundColor = Colors.orange.shade50;
        textColor = Colors.orange.shade800;
        iconColor = Colors.orange.shade600;
        icon = Icons.hourglass_empty;
        title = 'Application Under Review';
        message = 'Your reseller application is currently under review. We will contact you soon.';
        break;
      case ResellerApplicationStatus.approved:
        backgroundColor = Colors.green.shade50;
        textColor = Colors.green.shade800;
        iconColor = Colors.green.shade600;
        icon = Icons.check_circle;
        title = 'Application Approved!';
        message = 'Congratulations! Your reseller application has been approved. You can now work as a reseller.';
        break;
      case ResellerApplicationStatus.rejected:
        backgroundColor = Colors.red.shade50;
        textColor = Colors.red.shade800;
        iconColor = Colors.red.shade600;
        icon = Icons.cancel;
        title = 'Application Rejected';
        message = 'Sorry, your reseller application has been rejected. Please contact us for more information.';
        break;
      default:
        return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(
          color: iconColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: iconColor,
            size: 24,
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: textColor,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Text(
                  message,
                  style: TextStyle(
                    fontSize: 14,
                    color: textColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          GestureDetector(
            onTap: _dismissMessage,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              ),
              child: Icon(
                Icons.close,
                color: iconColor,
                size: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
