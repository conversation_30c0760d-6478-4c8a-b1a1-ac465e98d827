import '../models/user_model.dart';
import '../enums/user_role.dart';

class RoleAccessControl {
  // Check if user can access product dashboard
  static bool canAccessProductDashboard(UserModel? user) {
    if (user == null) return false;
    return user.canAccessProductDashboard;
  }

  // Check if user can upload products
  static bool canUploadProducts(UserModel? user) {
    if (user == null) return false;
    return user.canUploadProducts;
  }

  // Check if user can manage other users
  static bool canManageUsers(UserModel? user) {
    if (user == null) return false;
    return user.canManageUsers;
  }

  // Check if user can approve reseller applications
  static bool canApproveResellers(UserModel? user) {
    if (user == null) return false;
    return user.canApproveResellers;
  }

  // Check if user can apply for reseller
  static bool canApplyForReseller(UserModel? user) {
    if (user == null) return false;
    return user.canApplyForReseller;
  }

  // Get navigation items based on user role
  static List<String> getAvailableNavigationTabs(UserModel? user) {
    List<String> tabs = ['Feed', 'Search', 'Market', 'Chat'];
    
    // Profile tab is available for everyone
    tabs.add('Profile');
    
    return tabs;
  }

  // Check if user should see product dashboard button in profile
  static bool shouldShowProductDashboard(UserModel? user) {
    return canAccessProductDashboard(user);
  }

  // Check if user should see reseller application button
  static bool shouldShowResellerApplicationButton(UserModel? user) {
    return canApplyForReseller(user);
  }

  // Get role-specific profile tabs
  static List<String> getProfileTabs(UserModel? user) {
    List<String> tabs = [];
    
    if (user != null) {
      if (user.isUser) {
        // Regular users only see basic profile info
        tabs.add('Profile');
      } else if (user.isReseller || user.isAdmin) {
        // Resellers and Admins see profile tab
        tabs.add('Profile');
        tabs.add('Products');
        if (user.isAdmin) {
          tabs.add('Management');
        }
      }
    }
    
    return tabs;
  }

  // Check if user can view specific profile tab
  static bool canViewProfileTab(UserModel? user, String tabName) {
    if (user == null) return false;
    
    switch (tabName.toLowerCase()) {
      case 'profile':
        return true; // Everyone can see profile
      case 'products':
        return user.canAccessProductDashboard;
      case 'management':
        return user.canManageUsers;
      default:
        return false;
    }
  }

  // Get role display information
  static Map<String, dynamic> getRoleDisplayInfo(UserModel? user) {
    if (user == null) {
      return {
        'showIcon': false,
        'iconData': null,
        'color': null,
        'label': '',
      };
    }

    switch (user.role) {
      case UserRole.admin:
        return {
          'showIcon': true,
          'iconData': 'admin_panel_settings',
          'color': 0xFFE91E63, // Pink
          'label': 'Admin',
        };
      case UserRole.reseller:
        return {
          'showIcon': true,
          'iconData': 'store',
          'color': 0xFF2196F3, // Blue
          'label': 'Reseller',
        };
      case UserRole.user:
      default:
        return {
          'showIcon': false,
          'iconData': null,
          'color': null,
          'label': '',
        };
    }
  }

  // Check if user can perform specific actions
  static bool canPerformAction(UserModel? user, String action) {
    if (user == null) return false;

    switch (action.toLowerCase()) {
      case 'upload_product':
        return user.canUploadProducts;
      case 'access_dashboard':
        return user.canAccessProductDashboard;
      case 'manage_users':
        return user.canManageUsers;
      case 'approve_resellers':
        return user.canApproveResellers;
      case 'apply_reseller':
        return user.canApplyForReseller;
      case 'view_analytics':
        return user.isReseller || user.isAdmin;
      case 'moderate_content':
        return user.isAdmin;
      default:
        return false;
    }
  }

  // Get error message for unauthorized access
  static String getUnauthorizedMessage(String action) {
    switch (action.toLowerCase()) {
      case 'upload_product':
        return 'Only Resellers and Admins can upload products. Apply to become a Reseller to access this feature.';
      case 'access_dashboard':
        return 'Product dashboard is only available for Resellers and Admins.';
      case 'manage_users':
        return 'User management is only available for Admins.';
      case 'approve_resellers':
        return 'Only Admins can approve reseller applications.';
      default:
        return 'You do not have permission to perform this action.';
    }
  }
}
