Stack trace:
Frame         Function      Args
0007FFFFABB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9AB0) msys-2.0.dll+0x1FE8E
0007FFFFABB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE88) msys-2.0.dll+0x67F9
0007FFFFABB0  000210046832 (000210286019, 0007FFFFAA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFABB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFABB0  000210068E24 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAE90  00021006A225 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB61AB0000 ntdll.dll
7FFB5F7C0000 KERNEL32.DLL
7FFB5EE90000 KERNELBASE.dll
7FFB60340000 USER32.dll
7FFB5F270000 win32u.dll
7FFB60310000 GDI32.dll
7FFB5EC20000 gdi32full.dll
7FFB5F620000 msvcp_win.dll
7FFB5F2D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB60BE0000 advapi32.dll
7FFB60B30000 msvcrt.dll
7FFB619B0000 sechost.dll
7FFB5F2A0000 bcrypt.dll
7FFB5F8A0000 RPCRT4.dll
7FFB5E2D0000 CRYPTBASE.DLL
7FFB5F6C0000 bcryptPrimitives.dll
7FFB614F0000 IMM32.DLL
