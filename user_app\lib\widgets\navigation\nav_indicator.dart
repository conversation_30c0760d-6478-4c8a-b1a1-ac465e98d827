import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';

class NavIndicator extends StatefulWidget {
  final int itemCount;
  final int currentIndex;
  final Color? activeColor;
  final Color? inactiveColor;
  final double? width;
  final double? height;

  const NavIndicator({
    super.key,
    required this.itemCount,
    required this.currentIndex,
    this.activeColor,
    this.inactiveColor,
    this.width,
    this.height,
  });

  @override
  State<NavIndicator> createState() => _NavIndicatorState();
}

class _NavIndicatorState extends State<NavIndicator>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppConstants.animationDurationMedium,
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(NavIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.currentIndex != oldWidget.currentIndex) {
      _animationController.forward().then((_) {
        _animationController.reset();
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final indicatorWidth = widget.width ?? 200.0;
    final indicatorHeight = widget.height ?? 4.0;
    final itemWidth = indicatorWidth / widget.itemCount;

    return Container(
      width: indicatorWidth,
      height: indicatorHeight,
      decoration: BoxDecoration(
        color: widget.inactiveColor ?? AppConstants.textSecondaryColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(indicatorHeight / 2),
      ),
      child: Stack(
        children: [
          AnimatedPositioned(
            duration: AppConstants.animationDurationMedium,
            curve: Curves.easeInOut,
            left: widget.currentIndex * itemWidth,
            child: AnimatedBuilder(
              animation: _slideAnimation,
              builder: (context, child) {
                return Container(
                  width: itemWidth,
                  height: indicatorHeight,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        widget.activeColor ?? AppConstants.primaryColor,
                        (widget.activeColor ?? AppConstants.primaryColor).withOpacity(0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(indicatorHeight / 2),
                    boxShadow: [
                      BoxShadow(
                        color: (widget.activeColor ?? AppConstants.primaryColor).withOpacity(0.4),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class DotIndicator extends StatefulWidget {
  final int itemCount;
  final int currentIndex;
  final Color? activeColor;
  final Color? inactiveColor;
  final double? dotSize;
  final double? spacing;

  const DotIndicator({
    super.key,
    required this.itemCount,
    required this.currentIndex,
    this.activeColor,
    this.inactiveColor,
    this.dotSize,
    this.spacing,
  });

  @override
  State<DotIndicator> createState() => _DotIndicatorState();
}

class _DotIndicatorState extends State<DotIndicator>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: AppConstants.animationDurationMedium,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void didUpdateWidget(DotIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.currentIndex != oldWidget.currentIndex) {
      _scaleController.forward().then((_) {
        _scaleController.reverse();
      });
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dotSize = widget.dotSize ?? 8.0;
    final spacing = widget.spacing ?? 8.0;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(
        widget.itemCount,
        (index) => AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            final isActive = index == widget.currentIndex;
            final scale = isActive ? _scaleAnimation.value : 1.0;
            
            return Container(
              margin: EdgeInsets.symmetric(horizontal: spacing / 2),
              child: Transform.scale(
                scale: scale,
                child: Container(
                  width: dotSize,
                  height: dotSize,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isActive
                        ? widget.activeColor ?? AppConstants.primaryColor
                        : widget.inactiveColor ?? AppConstants.textSecondaryColor.withOpacity(0.3),
                    boxShadow: isActive
                        ? [
                            BoxShadow(
                              color: (widget.activeColor ?? AppConstants.primaryColor).withOpacity(0.4),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class WaveIndicator extends StatefulWidget {
  final int itemCount;
  final int currentIndex;
  final Color? activeColor;
  final Color? inactiveColor;
  final double? height;

  const WaveIndicator({
    super.key,
    required this.itemCount,
    required this.currentIndex,
    this.activeColor,
    this.inactiveColor,
    this.height,
  });

  @override
  State<WaveIndicator> createState() => _WaveIndicatorState();
}

class _WaveIndicatorState extends State<WaveIndicator>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;

  @override
  void initState() {
    super.initState();
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.easeInOut,
    ));
    _waveController.repeat();
  }

  @override
  void dispose() {
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final indicatorHeight = widget.height ?? 40.0;

    return SizedBox(
      height: indicatorHeight,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          widget.itemCount,
          (index) => AnimatedBuilder(
            animation: _waveAnimation,
            builder: (context, child) {
              final isActive = index == widget.currentIndex;
              final delay = index * 0.1;
              final animationValue = (_waveAnimation.value + delay) % 1.0;
              final height = isActive
                  ? indicatorHeight * (0.3 + 0.7 * (1 - (animationValue - 0.5).abs() * 2))
                  : indicatorHeight * 0.3;

              return Container(
                width: 4,
                height: height,
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  color: isActive
                      ? widget.activeColor ?? AppConstants.primaryColor
                      : widget.inactiveColor ?? AppConstants.textSecondaryColor.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
