import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../constants/app_constants.dart';
import '../../models/comment_model.dart';
import '../../services/comment_service.dart';

class CommentCardAdmin extends StatefulWidget {
  final CommentModel comment;
  final VoidCallback? onCommentUpdated;

  const CommentCardAdmin({
    super.key,
    required this.comment,
    this.onCommentUpdated,
  });

  @override
  State<CommentCardAdmin> createState() => _CommentCardAdminState();
}

class _CommentCardAdminState extends State<CommentCardAdmin> {
  bool _isUpdating = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          _buildContent(),
          _buildStats(),
          _buildActions(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundImage: widget.comment.userProfileImageUrl != null
                ? CachedNetworkImageProvider(widget.comment.userProfileImageUrl!)
                : null,
            child: widget.comment.userProfileImageUrl == null
                ? Text(
                    widget.comment.userDisplayName.isNotEmpty
                        ? widget.comment.userDisplayName[0].toUpperCase()
                        : 'U',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.comment.userDisplayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: AppConstants.fontSizeMedium,
                  ),
                ),
                Text(
                  '@${widget.comment.username}',
                  style: const TextStyle(
                    color: AppConstants.textSecondaryColor,
                    fontSize: AppConstants.fontSizeSmall,
                  ),
                ),
                Text(
                  'Post ID: ${widget.comment.postId}',
                  style: const TextStyle(
                    color: AppConstants.textSecondaryColor,
                    fontSize: AppConstants.fontSizeSmall,
                  ),
                ),
              ],
            ),
          ),
          _buildStatusBadge(),
        ],
      ),
    );
  }

  Widget _buildStatusBadge() {
    Color badgeColor;
    String badgeText;
    IconData badgeIcon;

    if (!widget.comment.isActive) {
      badgeColor = AppConstants.errorColor;
      badgeText = 'Inactive';
      badgeIcon = Icons.block;
    } else if (!widget.comment.isApproved) {
      badgeColor = AppConstants.warningColor;
      badgeText = 'Pending';
      badgeIcon = Icons.pending;
    } else {
      badgeColor = AppConstants.successColor;
      badgeText = 'Active';
      badgeIcon = Icons.check_circle;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            badgeIcon,
            size: 12,
            color: badgeColor,
          ),
          const SizedBox(width: 4),
          Text(
            badgeText,
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              fontWeight: FontWeight.w500,
              color: badgeColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: AppConstants.backgroundColor,
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              border: Border.all(color: AppConstants.borderColor),
            ),
            child: Text(
              widget.comment.content,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                height: 1.4,
              ),
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            widget.comment.timeAgo,
            style: const TextStyle(
              color: AppConstants.textSecondaryColor,
              fontSize: AppConstants.fontSizeSmall,
            ),
          ),
          if (widget.comment.moderatorNote != null) ...[
            const SizedBox(height: AppConstants.paddingSmall),
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingSmall),
              decoration: BoxDecoration(
                color: AppConstants.warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                border: Border.all(
                  color: AppConstants.warningColor.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.note_alt,
                    size: 16,
                    color: AppConstants.warningColor,
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: Text(
                      'Moderator Note: ${widget.comment.moderatorNote}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.warningColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStats() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Row(
        children: [
          _buildStatItem(
            icon: Icons.favorite,
            count: widget.comment.likeCount,
            label: 'Likes',
          ),
          const SizedBox(width: AppConstants.paddingLarge),
          _buildStatItem(
            icon: Icons.flag,
            count: 0, // This would come from reports
            label: 'Reports',
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required int count,
    required String label,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppConstants.textSecondaryColor,
        ),
        const SizedBox(width: 4),
        Text(
          '$count',
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: AppConstants.fontSizeSmall,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(
            color: AppConstants.textSecondaryColor,
            fontSize: AppConstants.fontSizeSmall,
          ),
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppConstants.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          if (!widget.comment.isApproved) ...[
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isUpdating ? null : () => _approveComment(),
                icon: const Icon(Icons.check, size: 16),
                label: const Text('Approve'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.successColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: AppConstants.paddingSmall),
          ],
          if (widget.comment.isActive) ...[
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isUpdating ? null : () => _deactivateComment(),
                icon: const Icon(Icons.block, size: 16),
                label: const Text('Deactivate'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.warningColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ] else ...[
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isUpdating ? null : () => _activateComment(),
                icon: const Icon(Icons.check_circle, size: 16),
                label: const Text('Activate'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.successColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
          const SizedBox(width: AppConstants.paddingSmall),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _isUpdating ? null : () => _deleteComment(),
              icon: const Icon(Icons.delete, size: 16),
              label: const Text('Delete'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.errorColor,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _approveComment() async {
    setState(() {
      _isUpdating = true;
    });

    try {
      final success = await CommentService.updateCommentStatus(
        commentId: widget.comment.id,
        isApproved: true,
        isActive: true,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment approved successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        widget.onCommentUpdated?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error approving comment: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _deactivateComment() async {
    setState(() {
      _isUpdating = true;
    });

    try {
      final success = await CommentService.updateCommentStatus(
        commentId: widget.comment.id,
        isApproved: widget.comment.isApproved,
        isActive: false,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment deactivated successfully'),
            backgroundColor: AppConstants.warningColor,
          ),
        );
        widget.onCommentUpdated?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deactivating comment: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _activateComment() async {
    setState(() {
      _isUpdating = true;
    });

    try {
      final success = await CommentService.updateCommentStatus(
        commentId: widget.comment.id,
        isApproved: true,
        isActive: true,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment activated successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        widget.onCommentUpdated?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error activating comment: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _deleteComment() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Comment'),
        content: const Text(
          'Are you sure you want to delete this comment? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      final success = await CommentService.deleteComment(widget.comment.id);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment deleted successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        widget.onCommentUpdated?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting comment: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }
}
