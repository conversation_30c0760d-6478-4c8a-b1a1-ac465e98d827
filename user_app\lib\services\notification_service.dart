import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/notification_model.dart';

class NotificationService {
  static const String _collection = 'notifications';
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Create a new notification
  static Future<bool> createNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required NotificationType type,
    required String title,
    required String message,
    String? relatedId,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Don't create notification for self
      if (userId == fromUserId) return false;

      final notificationId = _firestore.collection(_collection).doc().id;
      
      final notification = NotificationModel(
        id: notificationId,
        userId: userId,
        fromUserId: fromUserId,
        fromUserName: fromUserName,
        fromUserAvatar: fromUserAvatar,
        type: type,
        title: title,
        message: message,
        relatedId: relatedId,
        data: data,
        createdAt: DateTime.now(),
      );

      await _firestore
          .collection(_collection)
          .doc(notificationId)
          .set(notification.toMap());

      return true;
    } catch (e) {
      print('Error creating notification: $e');
      return false;
    }
  }

  // Get notifications for a user
  static Future<List<NotificationModel>> getUserNotifications(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(50)
          .get();

      return querySnapshot.docs
          .map((doc) => NotificationModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching notifications: $e');
      return [];
    }
  }

  // Get unread notifications count
  static Future<int> getUnreadNotificationsCount(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      print('Error fetching unread count: $e');
      return 0;
    }
  }

  // Mark notification as read
  static Future<bool> markAsRead(String notificationId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(notificationId)
          .update({
        'isRead': true,
        'readAt': Timestamp.fromDate(DateTime.now()),
      });

      return true;
    } catch (e) {
      print('Error marking notification as read: $e');
      return false;
    }
  }

  // Mark all notifications as read for a user
  static Future<bool> markAllAsRead(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .get();

      final batch = _firestore.batch();
      
      for (final doc in querySnapshot.docs) {
        batch.update(doc.reference, {
          'isRead': true,
          'readAt': Timestamp.fromDate(DateTime.now()),
        });
      }

      await batch.commit();
      return true;
    } catch (e) {
      print('Error marking all notifications as read: $e');
      return false;
    }
  }

  // Delete notification
  static Future<bool> deleteNotification(String notificationId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(notificationId)
          .delete();

      return true;
    } catch (e) {
      print('Error deleting notification: $e');
      return false;
    }
  }

  // Get real-time notifications stream
  static Stream<List<NotificationModel>> getNotificationsStream(String userId) {
    return _firestore
        .collection(_collection)
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => NotificationModel.fromMap(doc.data()))
            .toList());
  }

  // Get unread count stream
  static Stream<int> getUnreadCountStream(String userId) {
    return _firestore
        .collection(_collection)
        .where('userId', isEqualTo: userId)
        .where('isRead', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Helper methods for specific notification types

  // Create message notification
  static Future<bool> createMessageNotification({
    required String receiverId,
    required String senderId,
    required String senderName,
    required String senderAvatar,
    required String messageContent,
    required String chatId,
  }) async {
    return await createNotification(
      userId: receiverId,
      fromUserId: senderId,
      fromUserName: senderName,
      fromUserAvatar: senderAvatar,
      type: NotificationType.message,
      title: 'New Message',
      message: '$senderName sent you a message: ${messageContent.length > 50 ? '${messageContent.substring(0, 50)}...' : messageContent}',
      relatedId: chatId,
      data: {
        'chatId': chatId,
        'messageContent': messageContent,
      },
    );
  }

  // Create like notification
  static Future<bool> createLikeNotification({
    required String postOwnerId,
    required String likerId,
    required String likerName,
    required String likerAvatar,
    required String postId,
  }) async {
    return await createNotification(
      userId: postOwnerId,
      fromUserId: likerId,
      fromUserName: likerName,
      fromUserAvatar: likerAvatar,
      type: NotificationType.like,
      title: 'New Like',
      message: '$likerName liked your post',
      relatedId: postId,
      data: {
        'postId': postId,
      },
    );
  }

  // Create comment notification
  static Future<bool> createCommentNotification({
    required String postOwnerId,
    required String commenterId,
    required String commenterName,
    required String commenterAvatar,
    required String postId,
    required String commentContent,
  }) async {
    return await createNotification(
      userId: postOwnerId,
      fromUserId: commenterId,
      fromUserName: commenterName,
      fromUserAvatar: commenterAvatar,
      type: NotificationType.comment,
      title: 'New Comment',
      message: '$commenterName commented on your post: ${commentContent.length > 50 ? '${commentContent.substring(0, 50)}...' : commentContent}',
      relatedId: postId,
      data: {
        'postId': postId,
        'commentContent': commentContent,
      },
    );
  }

  // Create follow notification
  static Future<bool> createFollowNotification({
    required String followedUserId,
    required String followerId,
    required String followerName,
    required String followerAvatar,
  }) async {
    return await createNotification(
      userId: followedUserId,
      fromUserId: followerId,
      fromUserName: followerName,
      fromUserAvatar: followerAvatar,
      type: NotificationType.follow,
      title: 'New Follower',
      message: '$followerName started following you',
      data: {
        'followerId': followerId,
      },
    );
  }
}
