import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../models/analytics_model.dart';

class RecentActivities extends StatelessWidget {
  final AnalyticsModel analytics;

  const RecentActivities({
    super.key,
    required this.analytics,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Activities',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    // TODO: Navigate to full activities page
                  },
                  icon: const Icon(Icons.more_vert),
                  iconSize: AppConstants.iconSizeSmall,
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // Activities List
            Expanded(
              child: ListView(
                children: _buildActivities(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildActivities(BuildContext context) {
    final activities = <Widget>[];
    
    // Add today's activities based on analytics
    if (analytics.newUsersToday > 0) {
      activities.add(_buildActivityItem(
        context,
        icon: Icons.person_add,
        iconColor: AppConstants.primaryColor,
        title: 'New Users',
        subtitle: '${analytics.newUsersToday} users joined today',
        time: 'Today',
      ));
    }
    
    if (analytics.newPostsToday > 0) {
      activities.add(_buildActivityItem(
        context,
        icon: Icons.post_add,
        iconColor: AppConstants.successColor,
        title: 'New Posts',
        subtitle: '${analytics.newPostsToday} posts created today',
        time: 'Today',
      ));
    }
    
    if (analytics.newProductsToday > 0) {
      activities.add(_buildActivityItem(
        context,
        icon: Icons.shopping_bag,
        iconColor: AppConstants.warningColor,
        title: 'New Products',
        subtitle: '${analytics.newProductsToday} products added today',
        time: 'Today',
      ));
    }
    
    if (analytics.pendingResellerApplications > 0) {
      activities.add(_buildActivityItem(
        context,
        icon: Icons.pending_actions,
        iconColor: AppConstants.errorColor,
        title: 'Pending Applications',
        subtitle: '${analytics.pendingResellerApplications} reseller applications waiting',
        time: 'Pending',
        isUrgent: true,
      ));
    }
    
    // Add some sample activities if no real activities
    if (activities.isEmpty) {
      activities.addAll([
        _buildActivityItem(
          context,
          icon: Icons.analytics,
          iconColor: AppConstants.infoColor,
          title: 'System Update',
          subtitle: 'Analytics system updated successfully',
          time: '2 hours ago',
        ),
        _buildActivityItem(
          context,
          icon: Icons.security,
          iconColor: AppConstants.successColor,
          title: 'Security Check',
          subtitle: 'All security checks passed',
          time: '4 hours ago',
        ),
        _buildActivityItem(
          context,
          icon: Icons.backup,
          iconColor: AppConstants.primaryColor,
          title: 'Data Backup',
          subtitle: 'Daily backup completed',
          time: '6 hours ago',
        ),
      ]);
    }
    
    return activities;
  }

  Widget _buildActivityItem(
    BuildContext context, {
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required String time,
    bool isUrgent = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: isUrgent 
            ? AppConstants.errorColor.withOpacity(0.05)
            : AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: isUrgent 
            ? Border.all(color: AppConstants.errorColor.withOpacity(0.2))
            : null,
      ),
      child: Row(
        children: [
          // Icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: AppConstants.iconSizeSmall,
            ),
          ),
          
          const SizedBox(width: AppConstants.paddingMedium),
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isUrgent ? AppConstants.errorColor : null,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
          
          // Time
          Text(
            time,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppConstants.textHintColor,
              fontSize: AppConstants.fontSizeSmall - 1,
            ),
          ),
        ],
      ),
    );
  }
}
