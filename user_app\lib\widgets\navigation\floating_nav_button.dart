import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_constants.dart';

class FloatingNavButton extends StatefulWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final bool isSelected;
  final Color? color;
  final double? size;

  const FloatingNavButton({
    super.key,
    required this.icon,
    required this.label,
    required this.onTap,
    this.isSelected = false,
    this.color,
    this.size,
  });

  @override
  State<FloatingNavButton> createState() => _FloatingNavButtonState();
}

class _FloatingNavButtonState extends State<FloatingNavButton>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _scaleController = AnimationController(
      duration: AppConstants.animationDurationMedium,
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.15,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Start pulse animation if selected
    if (widget.isSelected) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(FloatingNavButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _pulseController.repeat(reverse: true);
      } else {
        _pulseController.stop();
        _pulseController.reset();
      }
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _handleTap() {
    HapticFeedback.mediumImpact();
    
    _scaleController.forward().then((_) {
      _scaleController.reverse();
    });
    
    _rotationController.forward().then((_) {
      _rotationController.reverse();
    });
    
    widget.onTap();
  }

  @override
  Widget build(BuildContext context) {
    final buttonColor = widget.color ?? AppConstants.primaryColor;
    final buttonSize = widget.size ?? 65.0;

    return GestureDetector(
      onTap: _handleTap,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _scaleAnimation,
          _rotationAnimation,
          _pulseAnimation,
        ]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value * 
                   (widget.isSelected ? _pulseAnimation.value : 1.0),
            child: Transform.rotate(
              angle: _rotationAnimation.value * 2 * 3.14159,
              child: Container(
                width: buttonSize,
                height: buttonSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      buttonColor,
                      buttonColor.withOpacity(0.8),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: buttonColor.withOpacity(0.4),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: buttonColor.withOpacity(0.2),
                      blurRadius: 30,
                      offset: const Offset(0, 20),
                      spreadRadius: 0,
                    ),
                    if (widget.isSelected)
                      BoxShadow(
                        color: buttonColor.withOpacity(0.6),
                        blurRadius: 25,
                        offset: const Offset(0, 0),
                        spreadRadius: 2,
                      ),
                  ],
                ),
                child: Stack(
                  children: [
                    // Glassmorphism overlay
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white.withOpacity(0.2),
                            Colors.white.withOpacity(0.1),
                          ],
                        ),
                      ),
                    ),
                    
                    // Icon
                    Center(
                      child: Icon(
                        widget.icon,
                        color: AppConstants.onPrimaryColor,
                        size: AppConstants.iconSizeLarge,
                      ),
                    ),
                    
                    // Ripple effect when selected
                    if (widget.isSelected)
                      Positioned.fill(
                        child: AnimatedBuilder(
                          animation: _pulseAnimation,
                          builder: (context, child) {
                            return Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: buttonColor.withOpacity(
                                    0.3 * (2 - _pulseAnimation.value)
                                  ),
                                  width: 2,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Alternative Floating Action Button with more features
class AdvancedFloatingButton extends StatefulWidget {
  final List<FloatingActionItem> items;
  final int selectedIndex;
  final Function(int) onItemSelected;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const AdvancedFloatingButton({
    super.key,
    required this.items,
    required this.selectedIndex,
    required this.onItemSelected,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  State<AdvancedFloatingButton> createState() => _AdvancedFloatingButtonState();
}

class FloatingActionItem {
  final IconData icon;
  final String label;
  final Color? color;

  const FloatingActionItem({
    required this.icon,
    required this.label,
    this.color,
  });
}

class _AdvancedFloatingButtonState extends State<AdvancedFloatingButton>
    with TickerProviderStateMixin {
  late AnimationController _expandController;
  late Animation<double> _expandAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _expandController = AnimationController(
      duration: AppConstants.animationDurationMedium,
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _expandController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _expandController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _expandController.forward();
    } else {
      _expandController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        // Expanded items
        AnimatedBuilder(
          animation: _expandAnimation,
          builder: (context, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ...List.generate(
                  widget.items.length,
                  (index) => Transform.translate(
                    offset: Offset(
                      0,
                      -_expandAnimation.value * (index + 1) * 70,
                    ),
                    child: Opacity(
                      opacity: _expandAnimation.value,
                      child: _buildExpandedItem(index),
                    ),
                  ),
                ),
                const SizedBox(height: 70), // Space for main button
              ],
            );
          },
        ),
        
        // Main floating button
        FloatingNavButton(
          icon: _isExpanded ? Icons.close : Icons.add,
          label: 'Actions',
          onTap: _toggleExpanded,
          isSelected: _isExpanded,
          color: widget.backgroundColor ?? AppConstants.primaryColor,
        ),
      ],
    );
  }

  Widget _buildExpandedItem(int index) {
    final item = widget.items[index];
    final isSelected = widget.selectedIndex == index;
    
    return GestureDetector(
      onTap: () {
        widget.onItemSelected(index);
        _toggleExpanded();
      },
      child: Container(
        width: 50,
        height: 50,
        margin: const EdgeInsets.only(bottom: 10),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: item.color ?? AppConstants.secondaryColor,
          boxShadow: [
            BoxShadow(
              color: (item.color ?? AppConstants.secondaryColor).withOpacity(0.3),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Icon(
          item.icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }
}
