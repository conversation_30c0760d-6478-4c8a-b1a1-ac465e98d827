import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../widgets/navigation/enhanced_bottom_nav.dart';
import '../widgets/navigation/floating_nav_button.dart';
import '../widgets/navigation/nav_indicator.dart';

class NavigationDemoScreen extends StatefulWidget {
  const NavigationDemoScreen({super.key});

  @override
  State<NavigationDemoScreen> createState() => _NavigationDemoScreenState();
}

class _NavigationDemoScreenState extends State<NavigationDemoScreen> {
  int _currentIndex = 0;
  int _indicatorIndex = 0;

  final List<EnhancedBottomNavItem> _navItems = [
    const EnhancedBottomNavItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Home',
    ),
    const EnhancedBottomNavItem(
      icon: Icons.search_outlined,
      activeIcon: Icons.search,
      label: 'Search',
    ),
    const EnhancedBottomNavItem(
      icon: Icons.favorite_outline,
      activeIcon: Icons.favorite,
      label: 'Favorites',
      badgeCount: 5,
    ),
    const EnhancedBottomNavItem(
      icon: Icons.notifications_outlined,
      activeIcon: Icons.notifications,
      label: 'Notifications',
      badgeCount: 12,
    ),
    const EnhancedBottomNavItem(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'Profile',
    ),
  ];

  final List<FloatingActionItem> _floatingItems = [
    const FloatingActionItem(
      icon: Icons.camera_alt,
      label: 'Camera',
      color: Colors.blue,
    ),
    const FloatingActionItem(
      icon: Icons.photo_library,
      label: 'Gallery',
      color: Colors.green,
    ),
    const FloatingActionItem(
      icon: Icons.video_call,
      label: 'Video',
      color: Colors.red,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Navigation Demo',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppConstants.textPrimaryColor,
          ),
        ),
        backgroundColor: AppConstants.surfaceColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('Enhanced Bottom Navigation'),
            const SizedBox(height: AppConstants.paddingMedium),
            _buildNavigationPreview(),
            
            const SizedBox(height: AppConstants.paddingLarge * 2),
            
            _buildSectionTitle('Navigation Indicators'),
            const SizedBox(height: AppConstants.paddingMedium),
            _buildIndicatorsDemo(),
            
            const SizedBox(height: AppConstants.paddingLarge * 2),
            
            _buildSectionTitle('Floating Action Buttons'),
            const SizedBox(height: AppConstants.paddingMedium),
            _buildFloatingButtonsDemo(),
            
            const SizedBox(height: AppConstants.paddingLarge * 2),
            
            _buildSectionTitle('Features'),
            const SizedBox(height: AppConstants.paddingMedium),
            _buildFeaturesList(),
            
            const SizedBox(height: 120), // Space for bottom navigation
          ],
        ),
      ),
      floatingActionButton: FloatingNavButton(
        icon: Icons.add,
        label: 'Add',
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Floating button tapped!'),
              duration: Duration(seconds: 2),
            ),
          );
        },
        isSelected: true,
      ),
      bottomNavigationBar: SafeArea(
        child: EnhancedBottomNav(
          items: _navItems,
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          height: 90,
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: AppConstants.fontSizeXLarge,
        fontWeight: FontWeight.bold,
        color: AppConstants.textPrimaryColor,
      ),
    );
  }

  Widget _buildNavigationPreview() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.surfaceColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppConstants.navShadowColor,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'Modern Bottom Navigation with:',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          const Text(
            '• Glassmorphism effect\n'
            '• Smooth animations\n'
            '• Badge notifications\n'
            '• Haptic feedback\n'
            '• Custom colors\n'
            '• Ripple effects',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIndicatorsDemo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.surfaceColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppConstants.navShadowColor,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'Line Indicator',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          NavIndicator(
            itemCount: 5,
            currentIndex: _indicatorIndex,
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          const Text(
            'Dot Indicator',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          DotIndicator(
            itemCount: 5,
            currentIndex: _indicatorIndex,
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          const Text(
            'Wave Indicator',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          WaveIndicator(
            itemCount: 5,
            currentIndex: _indicatorIndex,
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          ElevatedButton(
            onPressed: () {
              setState(() {
                _indicatorIndex = (_indicatorIndex + 1) % 5;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: AppConstants.onPrimaryColor,
            ),
            child: const Text('Next Indicator'),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingButtonsDemo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.surfaceColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppConstants.navShadowColor,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'Floating Action Buttons',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              FloatingNavButton(
                icon: Icons.camera_alt,
                label: 'Camera',
                onTap: () {},
                color: Colors.blue,
              ),
              FloatingNavButton(
                icon: Icons.favorite,
                label: 'Favorite',
                onTap: () {},
                color: Colors.red,
                isSelected: true,
              ),
              FloatingNavButton(
                icon: Icons.share,
                label: 'Share',
                onTap: () {},
                color: Colors.green,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesList() {
    final features = [
      'Glassmorphism design with blur effects',
      'Smooth animations and transitions',
      'Haptic feedback on interactions',
      'Badge notifications with custom styling',
      'Custom colors for different items',
      'Ripple effects and micro-interactions',
      'Responsive design for all screen sizes',
      'Easy to customize and extend',
    ];

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.surfaceColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppConstants.navShadowColor,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Key Features',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          ...features.map((feature) => Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppConstants.successColor,
                  size: AppConstants.iconSizeSmall,
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                Expanded(
                  child: Text(
                    feature,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }
}
