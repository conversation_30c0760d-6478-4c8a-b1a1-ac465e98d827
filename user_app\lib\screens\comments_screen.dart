import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_constants.dart';
import '../models/post_model.dart';
import '../models/comment_model.dart';
import '../services/comment_service.dart';
import '../providers/auth_provider.dart';
import '../widgets/role_indicator.dart';
import '../services/user_service.dart';
import '../models/user_model.dart';

class CommentsScreen extends StatefulWidget {
  final PostModel post;

  const CommentsScreen({
    super.key,
    required this.post,
  });

  @override
  State<CommentsScreen> createState() => _CommentsScreenState();
}

class _CommentsScreenState extends State<CommentsScreen> {
  final TextEditingController _commentController = TextEditingController();
  bool _isSubmitting = false;
  Map<String, UserModel> _commentUsers = {};
  UserModel? _postUser;

  @override
  void initState() {
    super.initState();
    _loadPostUser();
  }

  Future<void> _loadPostUser() async {
    try {
      final user = await UserService.getUserById(widget.post.userId);
      if (mounted && user != null) {
        setState(() {
          _postUser = user;
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _loadCommentUser(String userId) async {
    if (_commentUsers.containsKey(userId)) return;

    try {
      final user = await UserService.getUserById(userId);
      if (mounted && user != null) {
        setState(() {
          _commentUsers[userId] = user;
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Comments'),
        backgroundColor: AppConstants.surfaceColor,
      ),
      body: Column(
        children: [
          // Post Summary
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: const BoxDecoration(
              color: AppConstants.surfaceColor,
              border: Border(
                bottom: BorderSide(
                  color: AppConstants.backgroundColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: AppConstants.primaryColor,
                  backgroundImage: widget.post.userProfileImageUrl != null
                      ? CachedNetworkImageProvider(widget.post.userProfileImageUrl!)
                      : null,
                  child: widget.post.userProfileImageUrl == null
                      ? Text(
                          widget.post.userDisplayName.isNotEmpty
                              ? widget.post.userDisplayName[0].toUpperCase()
                              : 'U',
                          style: const TextStyle(
                            color: AppConstants.onPrimaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            widget.post.userDisplayName,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (_postUser != null) ...[
                            const SizedBox(width: 8),
                            RoleIndicator(
                              user: _postUser!,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      if (widget.post.content.isNotEmpty)
                        Text(
                          widget.post.content,
                          style: Theme.of(context).textTheme.bodyMedium,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Comments List
          Expanded(
            child: StreamBuilder<List<CommentModel>>(
              stream: CommentService.getCommentsStream(postId: widget.post.id),
              builder: (context, snapshot) {
                print('CommentsScreen: StreamBuilder state - connectionState: ${snapshot.connectionState}, hasError: ${snapshot.hasError}, hasData: ${snapshot.hasData}');

                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  print('CommentsScreen: Error in stream: ${snapshot.error}');
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline, size: 64, color: Colors.grey),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading comments',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Error: ${snapshot.error}',
                          style: Theme.of(context).textTheme.bodySmall,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: () => setState(() {}),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                final comments = snapshot.data ?? [];

                if (comments.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.comment_outlined, size: 64, color: Colors.grey),
                        const SizedBox(height: 16),
                        Text(
                          'No comments yet',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Be the first to comment!',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.fromLTRB(
                    0,
                    AppConstants.paddingSmall,
                    0,
                    100, // Bottom padding for navigation gap
                  ),
                  itemCount: comments.length,
                  itemBuilder: (context, index) {
                    return _buildCommentCard(comments[index]);
                  },
                );
              },
            ),
          ),

          // Comment Input
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: const BoxDecoration(
              color: AppConstants.surfaceColor,
              border: Border(
                top: BorderSide(
                  color: AppConstants.backgroundColor,
                  width: 1,
                ),
              ),
            ),
            child: Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                final currentUser = authProvider.currentUser;

                if (currentUser == null) {
                  return const Center(
                    child: Text(
                      'Please log in to comment',
                      style: TextStyle(color: Colors.grey),
                    ),
                  );
                }

                return Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: AppConstants.primaryColor,
                      backgroundImage: currentUser.profileImageUrl != null
                          ? CachedNetworkImageProvider(currentUser.profileImageUrl!)
                          : null,
                      child: currentUser.profileImageUrl == null
                          ? Text(
                              currentUser.displayName.isNotEmpty
                                  ? currentUser.displayName[0].toUpperCase()
                                  : 'U',
                              style: const TextStyle(
                                color: AppConstants.onPrimaryColor,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: TextField(
                        controller: _commentController,
                        decoration: const InputDecoration(
                          hintText: 'Write a comment...',
                          border: InputBorder.none,
                          filled: false,
                        ),
                        maxLines: null,
                        textInputAction: TextInputAction.send,
                        onSubmitted: (_) => _submitComment(currentUser),
                        enabled: !_isSubmitting,
                      ),
                    ),
                    IconButton(
                      onPressed: _isSubmitting ? null : () => _submitComment(currentUser),
                      icon: _isSubmitting
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.send),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentCard(CommentModel comment) {
    // Load user data for role indicator
    _loadCommentUser(comment.userId);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: AppConstants.primaryColor,
            backgroundImage: comment.userProfileImageUrl != null
                ? CachedNetworkImageProvider(comment.userProfileImageUrl!)
                : null,
            child: comment.userProfileImageUrl == null
                ? Text(
                    comment.userDisplayName.isNotEmpty
                        ? comment.userDisplayName[0].toUpperCase()
                        : 'U',
                    style: const TextStyle(
                      color: AppConstants.onPrimaryColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      comment.userDisplayName,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (_commentUsers.containsKey(comment.userId)) ...[
                      const SizedBox(width: 6),
                      RoleIndicator(
                        user: _commentUsers[comment.userId]!,
                        size: 12,
                      ),
                    ],
                    const SizedBox(width: 8),
                    Text(
                      comment.timeAgo,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  comment.content,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 8),
                Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    final currentUser = authProvider.currentUser;
                    final isLiked = currentUser != null && comment.isLikedBy(currentUser.id);

                    return Row(
                      children: [
                        GestureDetector(
                          onTap: currentUser != null
                              ? () => _toggleCommentLike(comment, currentUser.id)
                              : null,
                          child: Row(
                            children: [
                              Icon(
                                isLiked ? Icons.favorite : Icons.favorite_border,
                                size: 16,
                                color: isLiked ? Colors.red : Colors.grey,
                              ),
                              if (comment.likeCount > 0) ...[
                                const SizedBox(width: 4),
                                Text(
                                  '${comment.likeCount}',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        if (currentUser?.id == comment.userId) ...[
                          const SizedBox(width: 16),
                          GestureDetector(
                            onTap: () => _showCommentOptions(comment),
                            child: const Icon(
                              Icons.more_horiz,
                              size: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _submitComment(currentUser) async {
    final content = _commentController.text.trim();

    if (content.isEmpty) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final comment = await CommentService.createComment(
        postId: widget.post.id,
        user: currentUser,
        content: content,
      );

      if (comment != null) {
        _commentController.clear();
        _showSuccessSnackBar('Comment added successfully!');
      } else {
        _showErrorSnackBar('Failed to add comment. Please try again.');
      }
    } catch (e) {
      _showErrorSnackBar('Error adding comment: $e');
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  void _toggleCommentLike(CommentModel comment, String userId) async {
    try {
      await CommentService.toggleLike(
        commentId: comment.id,
        userId: userId,
      );
    } catch (e) {
      _showErrorSnackBar('Error updating like: $e');
    }
  }

  void _showCommentOptions(CommentModel comment) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('Edit Comment'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Implement edit functionality
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Delete Comment', style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.pop(context);
                  _deleteComment(comment);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _deleteComment(CommentModel comment) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Comment'),
          content: const Text('Are you sure you want to delete this comment?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      try {
        final success = await CommentService.deleteComment(
          commentId: comment.id,
          postId: widget.post.id,
        );
        if (success) {
          _showSuccessSnackBar('Comment deleted successfully');
        } else {
          _showErrorSnackBar('Failed to delete comment');
        }
      } catch (e) {
        _showErrorSnackBar('Error deleting comment: $e');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
