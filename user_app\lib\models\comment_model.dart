import 'package:cloud_firestore/cloud_firestore.dart';

class CommentModel {
  final String id;
  final String postId;
  final String userId;
  final String username;
  final String userDisplayName;
  final String? userProfileImageUrl;
  final String content;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> likes;
  final bool isActive;

  CommentModel({
    required this.id,
    required this.postId,
    required this.userId,
    required this.username,
    required this.userDisplayName,
    this.userProfileImageUrl,
    required this.content,
    required this.createdAt,
    required this.updatedAt,
    this.likes = const [],
    this.isActive = true,
  });

  // Convert CommentModel to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'postId': postId,
      'userId': userId,
      'username': username,
      'userDisplayName': userDisplayName,
      'userProfileImageUrl': userProfileImageUrl,
      'content': content,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'likes': likes,
      'isActive': isActive,
    };
  }

  // Create CommentModel from Firestore document
  factory CommentModel.fromMap(Map<String, dynamic> map) {
    return CommentModel(
      id: map['id'] ?? '',
      postId: map['postId'] ?? '',
      userId: map['userId'] ?? '',
      username: map['username'] ?? '',
      userDisplayName: map['userDisplayName'] ?? '',
      userProfileImageUrl: map['userProfileImageUrl'],
      content: map['content'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      likes: List<String>.from(map['likes'] ?? []),
      isActive: map['isActive'] ?? true,
    );
  }

  // Create CommentModel from Firestore DocumentSnapshot
  factory CommentModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return CommentModel.fromMap(data);
  }

  // Create a copy of CommentModel with updated fields
  CommentModel copyWith({
    String? id,
    String? postId,
    String? userId,
    String? username,
    String? userDisplayName,
    String? userProfileImageUrl,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? likes,
    bool? isActive,
  }) {
    return CommentModel(
      id: id ?? this.id,
      postId: postId ?? this.postId,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      userDisplayName: userDisplayName ?? this.userDisplayName,
      userProfileImageUrl: userProfileImageUrl ?? this.userProfileImageUrl,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likes: likes ?? this.likes,
      isActive: isActive ?? this.isActive,
    );
  }

  // Get like count
  int get likeCount => likes.length;

  // Check if comment is liked by user
  bool isLikedBy(String userId) => likes.contains(userId);

  // Get time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  @override
  String toString() {
    return 'CommentModel(id: $id, postId: $postId, userId: $userId, content: $content)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommentModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
