import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_constants.dart';
import '../../models/user_model.dart';

class StatisticsCard extends StatelessWidget {
  final UserModel? user;
  final int postsCount;
  final VoidCallback? onFollowersPressed;
  final VoidCallback? onFollowingPressed;
  final VoidCallback? onPostsPressed;

  const StatisticsCard({
    super.key,
    this.user,
    this.postsCount = 0,
    this.onFollowersPressed,
    this.onFollowingPressed,
    this.onPostsPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            '${user?.followerCount ?? 0}',
            'Followers',
            onFollowersPressed,
          ),
        ),
        const SizedBox(width: 1),
        Expanded(
          child: _buildStatCard(
            context,
            '${user?.followingCount ?? 0}',
            'Following',
            onFollowingPressed,
          ),
        ),
        const SizedBox(width: 1),
        Expanded(
          child: _buildStatCard(
            context,
            '$postsCount',
            'Posts',
            onPostsPressed,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String count,
    String label,
    VoidCallback? onPressed,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            count,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppConstants.textSecondaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
